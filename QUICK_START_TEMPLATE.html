<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>My Portfolio - 仿制模板</title>
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <style>
        /* 基础样式重置 */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Inter', 'Helvetica', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            overflow-x: hidden;
            cursor: none;
        }
        
        /* 自定义光标 */
        .cursor {
            position: fixed;
            width: 20px;
            height: 20px;
            background: rgba(255, 255, 255, 0.8);
            border-radius: 50%;
            pointer-events: none;
            z-index: 9999;
            transition: transform 0.1s ease;
        }
        
        /* 主容器 */
        .container {
            position: relative;
            min-height: 100vh;
        }
        
        /* 导航栏 */
        .navbar {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            padding: 2rem;
            z-index: 1000;
            backdrop-filter: blur(10px);
        }
        
        .nav-links {
            display: flex;
            justify-content: center;
            gap: 2rem;
            list-style: none;
        }
        
        .nav-links a {
            color: white;
            text-decoration: none;
            font-weight: 500;
            transition: opacity 0.3s ease;
        }
        
        .nav-links a:hover {
            opacity: 0.7;
        }
        
        /* 英雄区域 */
        .hero {
            height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            text-align: center;
            color: white;
        }
        
        .hero-content h1 {
            font-size: 4rem;
            font-weight: 700;
            margin-bottom: 1rem;
            opacity: 0;
            transform: translateY(50px);
        }
        
        .hero-content p {
            font-size: 1.2rem;
            opacity: 0;
            transform: translateY(30px);
        }
        
        /* 3D画布容器 */
        #three-canvas {
            position: fixed;
            top: 0;
            left: 0;
            z-index: -1;
        }
        
        /* 内容区域 */
        .section {
            min-height: 100vh;
            padding: 4rem 2rem;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .section-content {
            max-width: 800px;
            text-align: center;
            color: white;
        }
        
        .section h2 {
            font-size: 3rem;
            margin-bottom: 2rem;
            opacity: 0;
            transform: translateY(30px);
        }
        
        .section p {
            font-size: 1.1rem;
            line-height: 1.6;
            opacity: 0;
            transform: translateY(20px);
        }
        
        /* 卡片样式 */
        .card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 2rem;
            margin: 2rem 0;
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: transform 0.3s ease;
        }
        
        .card:hover {
            transform: translateY(-10px);
        }
        
        /* 响应式设计 */
        @media (max-width: 768px) {
            .hero-content h1 {
                font-size: 2.5rem;
            }
            
            .section h2 {
                font-size: 2rem;
            }
            
            .nav-links {
                gap: 1rem;
            }
        }
    </style>
</head>
<body>
    <!-- 自定义光标 -->
    <div class="cursor"></div>
    
    <!-- 3D画布 -->
    <canvas id="three-canvas"></canvas>
    
    <!-- 导航栏 -->
    <nav class="navbar">
        <ul class="nav-links">
            <li><a href="#home">首页</a></li>
            <li><a href="#about">关于</a></li>
            <li><a href="#work">作品</a></li>
            <li><a href="#contact">联系</a></li>
        </ul>
    </nav>
    
    <!-- 主内容 -->
    <div class="container">
        <!-- 英雄区域 -->
        <section id="home" class="hero">
            <div class="hero-content">
                <h1>你的名字</h1>
                <p>创意开发者 | 3D艺术家 | 前端工程师</p>
            </div>
        </section>
        
        <!-- 关于区域 -->
        <section id="about" class="section">
            <div class="section-content">
                <h2>关于我</h2>
                <p>我是一名充满激情的创意开发者，专注于创建令人惊叹的数字体验。结合现代Web技术和3D图形，我致力于推动交互设计的边界。</p>
                <div class="card">
                    <h3>技能专长</h3>
                    <p>Three.js • GSAP • WebGL • React • Node.js</p>
                </div>
            </div>
        </section>
        
        <!-- 作品区域 -->
        <section id="work" class="section">
            <div class="section-content">
                <h2>我的作品</h2>
                <div class="card">
                    <h3>项目一</h3>
                    <p>使用Three.js和GSAP创建的交互式3D体验</p>
                </div>
                <div class="card">
                    <h3>项目二</h3>
                    <p>响应式Web应用，具有复杂的动画效果</p>
                </div>
            </div>
        </section>
        
        <!-- 联系区域 -->
        <section id="contact" class="section">
            <div class="section-content">
                <h2>联系我</h2>
                <p>准备好开始你的下一个项目了吗？让我们一起创造一些令人惊叹的东西。</p>
                <div class="card">
                    <p>Email: <EMAIL></p>
                    <p>GitHub: github.com/yourusername</p>
                </div>
            </div>
        </section>
    </div>
    
    <!-- JavaScript库 -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/gsap.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/ScrollTrigger.min.js"></script>
    
    <script>
        // 注册GSAP插件
        gsap.registerPlugin(ScrollTrigger);
        
        // 自定义光标
        const cursor = document.querySelector('.cursor');
        document.addEventListener('mousemove', (e) => {
            cursor.style.left = e.clientX + 'px';
            cursor.style.top = e.clientY + 'px';
        });
        
        // Three.js场景设置
        const scene = new THREE.Scene();
        const camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
        const renderer = new THREE.WebGLRenderer({ 
            canvas: document.getElementById('three-canvas'),
            alpha: true 
        });
        
        renderer.setSize(window.innerWidth, window.innerHeight);
        renderer.setPixelRatio(Math.min(window.devicePixelRatio, 2));
        
        // 创建几何体
        const geometry = new THREE.IcosahedronGeometry(1, 1);
        const material = new THREE.MeshBasicMaterial({ 
            color: 0xffffff, 
            wireframe: true 
        });
        const mesh = new THREE.Mesh(geometry, material);
        scene.add(mesh);
        
        camera.position.z = 3;
        
        // 动画循环
        function animate() {
            requestAnimationFrame(animate);
            
            mesh.rotation.x += 0.01;
            mesh.rotation.y += 0.01;
            
            renderer.render(scene, camera);
        }
        animate();
        
        // 响应式处理
        window.addEventListener('resize', () => {
            camera.aspect = window.innerWidth / window.innerHeight;
            camera.updateProjectionMatrix();
            renderer.setSize(window.innerWidth, window.innerHeight);
        });
        
        // GSAP动画
        // 英雄区域动画
        gsap.timeline()
            .to('.hero-content h1', { duration: 1, opacity: 1, y: 0, ease: 'power2.out' })
            .to('.hero-content p', { duration: 1, opacity: 1, y: 0, ease: 'power2.out' }, '-=0.5');
        
        // 滚动触发动画
        gsap.utils.toArray('.section h2').forEach(heading => {
            gsap.to(heading, {
                scrollTrigger: {
                    trigger: heading,
                    start: 'top 80%',
                    end: 'bottom 20%',
                    scrub: false
                },
                opacity: 1,
                y: 0,
                duration: 1,
                ease: 'power2.out'
            });
        });
        
        gsap.utils.toArray('.section p').forEach(text => {
            gsap.to(text, {
                scrollTrigger: {
                    trigger: text,
                    start: 'top 80%',
                    end: 'bottom 20%',
                    scrub: false
                },
                opacity: 1,
                y: 0,
                duration: 1,
                delay: 0.2,
                ease: 'power2.out'
            });
        });
        
        // 卡片悬停效果
        document.querySelectorAll('.card').forEach(card => {
            card.addEventListener('mouseenter', () => {
                gsap.to(card, { duration: 0.3, scale: 1.05, ease: 'power2.out' });
            });
            
            card.addEventListener('mouseleave', () => {
                gsap.to(card, { duration: 0.3, scale: 1, ease: 'power2.out' });
            });
        });
        
        // 平滑滚动
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    gsap.to(window, { duration: 1, scrollTo: target, ease: 'power2.inOut' });
                }
            });
        });
    </script>
</body>
</html>
