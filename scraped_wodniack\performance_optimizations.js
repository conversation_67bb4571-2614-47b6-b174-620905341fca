/**
 * Performance Optimizations for Wodniack.dev
 * Implements lazy loading, debouncing, and other performance improvements
 */

(function() {
    'use strict';
    
    console.log('🚀 Loading performance optimizations...');
    
    // 1. Video Lazy Loading with Intersection Observer
    class VideoLazyLoader {
        constructor() {
            this.videos = document.querySelectorAll('video[data-src]');
            this.loadedVideos = new Set();
            this.init();
        }
        
        init() {
            if ('IntersectionObserver' in window) {
                this.observer = new IntersectionObserver(
                    this.handleIntersection.bind(this),
                    {
                        rootMargin: '50px 0px',
                        threshold: 0.1
                    }
                );
                
                this.videos.forEach(video => {
                    video.setAttribute('data-loaded', 'false');
                    this.observer.observe(video);
                });
            } else {
                // Fallback for older browsers
                this.loadAllVideos();
            }
        }
        
        handleIntersection(entries) {
            entries.forEach(entry => {
                if (entry.isIntersecting && !this.loadedVideos.has(entry.target)) {
                    this.loadVideo(entry.target);
                }
            });
        }
        
        loadVideo(video) {
            const src = video.getAttribute('data-src');
            if (src) {
                video.src = src;
                video.setAttribute('data-loaded', 'true');
                this.loadedVideos.add(video);
                this.observer.unobserve(video);
                
                // Add load event listener
                video.addEventListener('loadeddata', () => {
                    console.log('✅ Video loaded:', src.split('/').pop());
                });
                
                video.addEventListener('error', () => {
                    console.warn('❌ Video failed to load:', src.split('/').pop());
                });
            }
        }
        
        loadAllVideos() {
            this.videos.forEach(video => this.loadVideo(video));
        }
    }
    
    // 2. Debounced Scroll Handler
    class ScrollOptimizer {
        constructor() {
            this.scrollHandlers = [];
            this.isScrolling = false;
            this.init();
        }
        
        init() {
            window.addEventListener('scroll', this.handleScroll.bind(this), { passive: true });
        }
        
        handleScroll() {
            if (!this.isScrolling) {
                this.isScrolling = true;
                requestAnimationFrame(() => {
                    this.scrollHandlers.forEach(handler => handler());
                    this.isScrolling = false;
                });
            }
        }
        
        addHandler(handler) {
            this.scrollHandlers.push(handler);
        }
    }
    
    // 3. Binary Character Animation Optimizer
    class BinaryAnimationOptimizer {
        constructor() {
            this.chars = document.querySelectorAll('.js-char');
            this.animationFrame = null;
            this.init();
        }
        
        init() {
            // Reduce the number of animated characters for better performance
            const maxAnimatedChars = Math.min(this.chars.length, 50);
            const step = Math.floor(this.chars.length / maxAnimatedChars);
            
            this.chars.forEach((char, index) => {
                if (index % step === 0) {
                    this.setupCharAnimation(char);
                }
            });
        }
        
        setupCharAnimation(char) {
            let isAnimating = false;
            
            char.addEventListener('mouseenter', () => {
                if (!isAnimating) {
                    isAnimating = true;
                    this.animateChar(char, () => {
                        isAnimating = false;
                    });
                }
            });
        }
        
        animateChar(char, callback) {
            const originalTransform = char.style.transform;
            char.style.transform = 'scale(1.1) rotate(5deg)';
            char.style.color = '#ff6b6b';
            
            setTimeout(() => {
                char.style.transform = originalTransform;
                char.style.color = '';
                callback();
            }, 200);
        }
    }
    
    // 4. Memory Management
    class MemoryManager {
        constructor() {
            this.cleanupTasks = [];
            this.init();
        }
        
        init() {
            // Clean up unused resources periodically
            setInterval(() => {
                this.cleanup();
            }, 30000); // Every 30 seconds
            
            // Clean up on page unload
            window.addEventListener('beforeunload', () => {
                this.cleanup();
            });
        }
        
        addCleanupTask(task) {
            this.cleanupTasks.push(task);
        }
        
        cleanup() {
            this.cleanupTasks.forEach(task => {
                try {
                    task();
                } catch (e) {
                    console.warn('Cleanup task failed:', e);
                }
            });
        }
    }
    
    // 5. Performance Monitor
    class PerformanceMonitor {
        constructor() {
            this.metrics = {
                videoLoadTime: [],
                scrollPerformance: [],
                memoryUsage: []
            };
            this.init();
        }
        
        init() {
            // Monitor performance periodically
            setInterval(() => {
                this.collectMetrics();
            }, 10000); // Every 10 seconds
        }
        
        collectMetrics() {
            if (performance.memory) {
                this.metrics.memoryUsage.push({
                    timestamp: Date.now(),
                    used: performance.memory.usedJSHeapSize,
                    total: performance.memory.totalJSHeapSize
                });
                
                // Keep only last 10 measurements
                if (this.metrics.memoryUsage.length > 10) {
                    this.metrics.memoryUsage.shift();
                }
            }
        }
        
        logPerformance() {
            console.group('📊 Performance Metrics');
            console.log('Videos loaded:', document.querySelectorAll('video[data-loaded="true"]').length);
            console.log('Total videos:', document.querySelectorAll('video').length);
            if (this.metrics.memoryUsage.length > 0) {
                const latest = this.metrics.memoryUsage[this.metrics.memoryUsage.length - 1];
                console.log('Memory usage:', Math.round(latest.used / 1024 / 1024) + 'MB');
            }
            console.groupEnd();
        }
    }
    
    // Initialize all optimizations when DOM is ready
    function initOptimizations() {
        console.log('🎯 Initializing performance optimizations...');
        
        const videoLoader = new VideoLazyLoader();
        const scrollOptimizer = new ScrollOptimizer();
        const binaryOptimizer = new BinaryAnimationOptimizer();
        const memoryManager = new MemoryManager();
        const performanceMonitor = new PerformanceMonitor();
        
        // Log performance metrics every 30 seconds
        setInterval(() => {
            performanceMonitor.logPerformance();
        }, 30000);
        
        console.log('✅ Performance optimizations loaded successfully!');
    }
    
    // Wait for DOM to be ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initOptimizations);
    } else {
        initOptimizations();
    }
    
})();
