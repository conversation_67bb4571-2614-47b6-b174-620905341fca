/**
 * Performance Optimizations for Wodniack.dev
 * Implements lazy loading, debouncing, and other performance improvements
 */

(function() {
    'use strict';
    
    console.log('🚀 Loading performance optimizations...');
    
    // 1. Video Lazy Loading with Intersection Observer
    class VideoLazyLoader {
        constructor() {
            this.videos = document.querySelectorAll('video[data-src]');
            this.loadedVideos = new Set();
            this.init();
        }
        
        init() {
            if ('IntersectionObserver' in window) {
                this.observer = new IntersectionObserver(
                    this.handleIntersection.bind(this),
                    {
                        rootMargin: '50px 0px',
                        threshold: 0.1
                    }
                );
                
                this.videos.forEach(video => {
                    video.setAttribute('data-loaded', 'false');
                    this.observer.observe(video);
                });
            } else {
                // Fallback for older browsers
                this.loadAllVideos();
            }
        }
        
        handleIntersection(entries) {
            entries.forEach(entry => {
                if (entry.isIntersecting && !this.loadedVideos.has(entry.target)) {
                    this.loadVideo(entry.target);
                }
            });
        }
        
        loadVideo(video) {
            const src = video.getAttribute('data-src');
            if (src) {
                video.src = src;
                video.setAttribute('data-loaded', 'true');
                this.loadedVideos.add(video);
                this.observer.unobserve(video);
                
                // Add load event listener
                video.addEventListener('loadeddata', () => {
                    console.log('✅ Video loaded:', src.split('/').pop());
                });
                
                video.addEventListener('error', () => {
                    console.warn('❌ Video failed to load:', src.split('/').pop());
                });
            }
        }
        
        loadAllVideos() {
            this.videos.forEach(video => this.loadVideo(video));
        }
    }
    
    // 2. Mouse and Scroll Performance Optimizer
    class MouseScrollOptimizer {
        constructor() {
            this.isScrolling = false;
            this.mouseThrottled = false;
            this.init();
        }

        init() {
            // Optimize scroll performance
            window.addEventListener('scroll', this.handleScroll.bind(this), { passive: true });

            // Disable problematic mouse events on binary characters
            this.disableExpensiveMouseEvents();

            // Add performance monitoring
            this.monitorPerformance();
        }

        handleScroll() {
            if (!this.isScrolling) {
                this.isScrolling = true;
                requestAnimationFrame(() => {
                    this.isScrolling = false;
                });
            }
        }

        disableExpensiveMouseEvents() {
            // Remove all expensive mouse event listeners from binary characters
            const binaryChars = document.querySelectorAll('.js-char, [class*="char"]');
            console.log(`🚫 Disabling expensive mouse events on ${binaryChars.length} elements`);

            binaryChars.forEach(char => {
                // Remove existing event listeners by cloning the element
                const newChar = char.cloneNode(true);
                char.parentNode.replaceChild(newChar, char);

                // Reset styles to prevent lag
                newChar.style.transition = 'none';
                newChar.style.transform = 'none';
                newChar.style.cursor = 'default';
            });
        }

        monitorPerformance() {
            let frameCount = 0;
            let lastTime = performance.now();

            const checkFPS = () => {
                frameCount++;
                const currentTime = performance.now();

                if (currentTime - lastTime >= 1000) {
                    const fps = Math.round((frameCount * 1000) / (currentTime - lastTime));
                    if (fps < 30) {
                        console.warn(`⚠️ Low FPS detected: ${fps}fps`);
                    }
                    frameCount = 0;
                    lastTime = currentTime;
                }

                requestAnimationFrame(checkFPS);
            };

            requestAnimationFrame(checkFPS);
        }
    }
    
    // 3. DISABLED: Binary Character Animation (causes severe lag)
    class BinaryAnimationOptimizer {
        constructor() {
            console.log('🚫 Binary character animations DISABLED for performance');
            // All binary character animations disabled to prevent mouse lag
        }
    }
    
    // 4. Memory Management
    class MemoryManager {
        constructor() {
            this.cleanupTasks = [];
            this.init();
        }
        
        init() {
            // Clean up unused resources periodically
            setInterval(() => {
                this.cleanup();
            }, 30000); // Every 30 seconds
            
            // Clean up on page unload
            window.addEventListener('beforeunload', () => {
                this.cleanup();
            });
        }
        
        addCleanupTask(task) {
            this.cleanupTasks.push(task);
        }
        
        cleanup() {
            this.cleanupTasks.forEach(task => {
                try {
                    task();
                } catch (e) {
                    console.warn('Cleanup task failed:', e);
                }
            });
        }
    }
    
    // 5. Performance Monitor
    class PerformanceMonitor {
        constructor() {
            this.metrics = {
                videoLoadTime: [],
                scrollPerformance: [],
                memoryUsage: []
            };
            this.init();
        }
        
        init() {
            // Monitor performance periodically
            setInterval(() => {
                this.collectMetrics();
            }, 10000); // Every 10 seconds
        }
        
        collectMetrics() {
            if (performance.memory) {
                this.metrics.memoryUsage.push({
                    timestamp: Date.now(),
                    used: performance.memory.usedJSHeapSize,
                    total: performance.memory.totalJSHeapSize
                });
                
                // Keep only last 10 measurements
                if (this.metrics.memoryUsage.length > 10) {
                    this.metrics.memoryUsage.shift();
                }
            }
        }
        
        logPerformance() {
            console.group('📊 Performance Metrics');
            console.log('Videos loaded:', document.querySelectorAll('video[data-loaded="true"]').length);
            console.log('Total videos:', document.querySelectorAll('video').length);
            if (this.metrics.memoryUsage.length > 0) {
                const latest = this.metrics.memoryUsage[this.metrics.memoryUsage.length - 1];
                console.log('Memory usage:', Math.round(latest.used / 1024 / 1024) + 'MB');
            }
            console.groupEnd();
        }
    }
    
    // Initialize all optimizations when DOM is ready
    function initOptimizations() {
        console.log('🎯 Initializing AGGRESSIVE performance optimizations...');
        console.log('🚫 Disabling all expensive animations and interactions');

        const videoLoader = new VideoLazyLoader();
        const mouseScrollOptimizer = new MouseScrollOptimizer();
        const binaryOptimizer = new BinaryAnimationOptimizer();
        const memoryManager = new MemoryManager();
        const performanceMonitor = new PerformanceMonitor();

        // Disable all CSS animations and transitions globally
        const style = document.createElement('style');
        style.textContent = `
            *, *::before, *::after {
                animation-duration: 0.01ms !important;
                animation-iteration-count: 1 !important;
                transition-duration: 0.01ms !important;
                transition-delay: 0ms !important;
            }

            .js-char, [class*="char"] {
                pointer-events: none !important;
                transition: none !important;
                transform: none !important;
            }
        `;
        document.head.appendChild(style);

        // Log performance metrics every 30 seconds
        setInterval(() => {
            performanceMonitor.logPerformance();
        }, 30000);

        console.log('✅ AGGRESSIVE performance optimizations loaded!');
        console.log('🎯 Mouse lag should be eliminated');
    }
    
    // Wait for DOM to be ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initOptimizations);
    } else {
        initOptimizations();
    }
    
})();
