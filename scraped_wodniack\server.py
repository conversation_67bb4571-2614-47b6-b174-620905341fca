#!/usr/bin/env python3
"""
Optimized HTTP server for the scraped wodniack.dev website
Run: python server.py
Then open: http://localhost:8001
"""

import http.server
import socketserver
import os
import webbrowser
import gzip
import mimetypes
from pathlib import Path

PORT = 8001

class OptimizedHTTPRequestHandler(http.server.SimpleHTTPRequestHandler):
    def end_headers(self):
        # Enable caching for static assets
        if self.path.endswith(('.css', '.js', '.woff2', '.svg', '.png', '.jpg', '.jpeg', '.webp')):
            self.send_header('Cache-Control', 'public, max-age=86400')  # 1 day cache
        elif self.path.endswith('.mp4'):
            self.send_header('Cache-Control', 'public, max-age=3600')   # 1 hour cache for videos
        else:
            self.send_header('Cache-Control', 'no-cache, no-store, must-revalidate')

        # Enable compression for text files
        if self.path.endswith(('.html', '.css', '.js', '.svg')):
            self.send_header('Content-Encoding', 'gzip')

        super().end_headers()

    def do_GET(self):
        # Handle gzip compression for text files
        if self.path.endswith(('.html', '.css', '.js', '.svg')):
            try:
                file_path = self.translate_path(self.path)
                if os.path.exists(file_path):
                    with open(file_path, 'rb') as f:
                        content = f.read()

                    # Compress content
                    compressed_content = gzip.compress(content)

                    # Send response
                    self.send_response(200)
                    self.send_header('Content-Type', mimetypes.guess_type(file_path)[0] or 'text/plain')
                    self.send_header('Content-Length', str(len(compressed_content)))
                    self.end_headers()
                    self.wfile.write(compressed_content)
                    return
            except Exception:
                pass  # Fall back to default handling

        # Default handling for other files
        super().do_GET()

if __name__ == "__main__":
    os.chdir(os.path.dirname(os.path.abspath(__file__)))

    with socketserver.TCPServer(("", PORT), OptimizedHTTPRequestHandler) as httpd:
        print(f"🚀 Serving optimized wodniack.dev at http://localhost:{PORT}")
        print("✨ Features enabled:")
        print("   - Gzip compression for text files")
        print("   - Smart caching for static assets")
        print("   - Video lazy loading support")
        print("Press Ctrl+C to stop the server")

        try:
            webbrowser.open(f'http://localhost:{PORT}')
        except:
            pass

        httpd.serve_forever()
