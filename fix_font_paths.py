#!/usr/bin/env python3
"""
Fix font paths in CSS files
"""

import os
from pathlib import Path

def fix_css_font_paths():
    """Fix font paths in CSS files"""
    css_file = Path("scraped_ohmyking_home/css/css2.css")
    
    if css_file.exists():
        try:
            with open(css_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            print("Original CSS content (first few lines):")
            print(content[:500])
            print("\n" + "="*50 + "\n")
            
            # Fix all font paths - remove any backslashes and fix the path structure
            content = content.replace('url("../assets/u-4X0qWljRw-PfU81xCKCpdpbgZJl6XFpfEd7eA9BIxxkV2EH7mlx17r.woff2")', 'url("../assets/u-4X0qWljRw-PfU81xCKCpdpbgZJl6XFpfEd7eA9BIxxkV2EH7mlx17r.woff2")')
            content = content.replace('url("../assets/u-4X0qWljRw-PfU81xCKCpdpbgZJl6XFpfEd7eA9BIxxkV2EH7alxw.woff2")', 'url("../assets/u-4X0qWljRw-PfU81xCKCpdpbgZJl6XFpfEd7eA9BIxxkV2EH7alxw.woff2")')
            
            # Also try alternative paths
            content = content.replace('url("assets\\', 'url("../assets/')
            content = content.replace('url("assets/', 'url("../assets/')
            
            print("Fixed CSS content (first few lines):")
            print(content[:500])
            
            with open(css_file, 'w', encoding='utf-8') as f:
                f.write(content)
            
            print(f"✓ Fixed font paths in: {css_file}")
            
        except Exception as e:
            print(f"✗ Failed to fix font paths: {e}")
    
    # Also check if font files exist
    assets_dir = Path("scraped_ohmyking_home/assets")
    if assets_dir.exists():
        print(f"\nFont files in assets directory:")
        for font_file in assets_dir.glob("*.woff2"):
            print(f"  - {font_file.name}")
    else:
        print("Assets directory not found!")

if __name__ == "__main__":
    fix_css_font_paths()
