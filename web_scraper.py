#!/usr/bin/env python3
"""
Web Scraper for JavaScript-rendered websites
Scrapes https://ohmyking.github.io/home/<USER>
"""

import os
import re
import asyncio
import requests
from urllib.parse import urljoin, urlparse, unquote
from pathlib import Path
from playwright.async_api import async_playwright
from bs4 import BeautifulSoup
import mimetypes
import json

class WebScraper:
    def __init__(self, base_url, output_dir="scraped_website"):
        self.base_url = base_url
        self.output_dir = Path(output_dir)
        self.downloaded_files = set()
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        })
        
    def create_directories(self):
        """Create output directory structure"""
        self.output_dir.mkdir(exist_ok=True)
        (self.output_dir / "assets").mkdir(exist_ok=True)
        (self.output_dir / "css").mkdir(exist_ok=True)
        (self.output_dir / "js").mkdir(exist_ok=True)
        (self.output_dir / "images").mkdir(exist_ok=True)
        (self.output_dir / "fonts").mkdir(exist_ok=True)
        
    def get_file_extension(self, url, content_type=None):
        """Get appropriate file extension based on URL and content type"""
        parsed = urlparse(url)
        path = unquote(parsed.path)
        
        if '.' in path.split('/')[-1]:
            return Path(path).suffix
        
        if content_type:
            ext = mimetypes.guess_extension(content_type.split(';')[0])
            if ext:
                return ext
                
        # Default extensions for common types
        if 'javascript' in str(content_type).lower() or url.endswith('.js'):
            return '.js'
        elif 'css' in str(content_type).lower() or url.endswith('.css'):
            return '.css'
        elif any(img_type in str(content_type).lower() for img_type in ['image', 'png', 'jpg', 'jpeg', 'gif', 'svg', 'webp']):
            return '.png'  # Default to png for images
        elif 'font' in str(content_type).lower():
            return '.woff2'
            
        return '.txt'
    
    def sanitize_filename(self, filename):
        """Sanitize filename for filesystem"""
        # Remove or replace invalid characters
        filename = re.sub(r'[<>:"/\\|?*]', '_', filename)
        filename = re.sub(r'[^\w\-_\.]', '_', filename)
        return filename[:100]  # Limit length
    
    def download_resource(self, url, subdir="assets"):
        """Download a resource and return local path"""
        if url in self.downloaded_files:
            return self.get_local_path(url, subdir)
            
        try:
            print(f"Downloading: {url}")
            response = self.session.get(url, timeout=30)
            response.raise_for_status()
            
            # Determine file extension
            content_type = response.headers.get('content-type', '')
            ext = self.get_file_extension(url, content_type)
            
            # Create filename
            parsed = urlparse(url)
            filename = parsed.path.split('/')[-1] or 'index'
            filename = unquote(filename)
            
            if not filename.endswith(ext):
                filename = self.sanitize_filename(filename) + ext
            else:
                filename = self.sanitize_filename(filename)
            
            # Save file
            file_path = self.output_dir / subdir / filename
            
            # Handle duplicate filenames
            counter = 1
            original_path = file_path
            while file_path.exists():
                name_part = original_path.stem
                ext_part = original_path.suffix
                file_path = original_path.parent / f"{name_part}_{counter}{ext_part}"
                counter += 1
            
            with open(file_path, 'wb') as f:
                f.write(response.content)
                
            self.downloaded_files.add(url)
            relative_path = file_path.relative_to(self.output_dir)
            print(f"Saved: {relative_path}")
            return str(relative_path)
            
        except Exception as e:
            print(f"Failed to download {url}: {e}")
            return url  # Return original URL as fallback
    
    def get_local_path(self, url, subdir="assets"):
        """Get local path for already downloaded resource"""
        parsed = urlparse(url)
        filename = parsed.path.split('/')[-1] or 'index'
        filename = unquote(filename)
        
        # Try to find existing file
        search_dir = self.output_dir / subdir
        for file_path in search_dir.glob(f"{self.sanitize_filename(filename)}*"):
            return str(file_path.relative_to(self.output_dir))
        
        return url  # Fallback to original URL
    
    def process_css(self, css_content, css_url):
        """Process CSS content to download referenced resources"""
        # Find URLs in CSS (url(), @import, etc.)
        url_pattern = r'url\(["\']?([^"\')\s]+)["\']?\)'
        import_pattern = r'@import\s+["\']([^"\']+)["\']'
        
        def replace_url(match):
            resource_url = match.group(1)
            if resource_url.startswith(('data:', 'http://', 'https://')):
                if resource_url.startswith(('http://', 'https://')):
                    local_path = self.download_resource(resource_url, "assets")
                    return f'url("{local_path}")'
                return match.group(0)  # Keep data URLs as-is
            else:
                full_url = urljoin(css_url, resource_url)
                local_path = self.download_resource(full_url, "assets")
                return f'url("{local_path}")'
        
        def replace_import(match):
            resource_url = match.group(1)
            if not resource_url.startswith(('http://', 'https://')):
                resource_url = urljoin(css_url, resource_url)
            local_path = self.download_resource(resource_url, "css")
            return f'@import "{local_path}"'
        
        css_content = re.sub(url_pattern, replace_url, css_content)
        css_content = re.sub(import_pattern, replace_import, css_content)
        
        return css_content
    
    async def scrape_website(self):
        """Main scraping function"""
        print(f"Starting to scrape: {self.base_url}")
        self.create_directories()
        
        async with async_playwright() as p:
            # Launch browser
            browser = await p.chromium.launch(headless=False)  # Set to True for headless mode
            page = await browser.new_page()
            
            # Set viewport and user agent
            await page.set_viewport_size({"width": 1920, "height": 1080})
            
            try:
                # Navigate to page
                print("Loading page...")
                await page.goto(self.base_url, wait_until="networkidle", timeout=60000)
                
                # Wait for JavaScript to render
                print("Waiting for JavaScript to render...")
                await page.wait_for_timeout(5000)
                
                # Get page content after JS rendering
                html_content = await page.content()
                
                # Parse HTML
                soup = BeautifulSoup(html_content, 'html.parser')
                
                print("Processing resources...")
                
                # Download CSS files
                for link in soup.find_all('link', rel='stylesheet'):
                    href = link.get('href')
                    if href:
                        css_url = urljoin(self.base_url, href)
                        local_path = self.download_resource(css_url, "css")
                        
                        # Process CSS content for embedded resources
                        try:
                            css_response = self.session.get(css_url)
                            css_content = self.process_css(css_response.text, css_url)
                            
                            # Save processed CSS
                            css_file_path = self.output_dir / local_path
                            with open(css_file_path, 'w', encoding='utf-8') as f:
                                f.write(css_content)
                        except Exception as e:
                            print(f"Error processing CSS {css_url}: {e}")
                        
                        link['href'] = local_path
                
                # Download JavaScript files
                for script in soup.find_all('script', src=True):
                    src = script.get('src')
                    if src:
                        js_url = urljoin(self.base_url, src)
                        local_path = self.download_resource(js_url, "js")
                        script['src'] = local_path
                
                # Download images
                for img in soup.find_all('img', src=True):
                    src = img.get('src')
                    if src and not src.startswith('data:'):
                        img_url = urljoin(self.base_url, src)
                        local_path = self.download_resource(img_url, "images")
                        img['src'] = local_path
                
                # Handle other resources (fonts, etc.)
                for link in soup.find_all('link'):
                    href = link.get('href')
                    if href and link.get('rel') not in ['stylesheet']:
                        resource_url = urljoin(self.base_url, href)
                        local_path = self.download_resource(resource_url, "assets")
                        link['href'] = local_path
                
                # Save the main HTML file
                html_file = self.output_dir / "index.html"
                with open(html_file, 'w', encoding='utf-8') as f:
                    f.write(str(soup))
                
                print(f"Main HTML saved: {html_file}")
                
                # Get network requests to catch any missed resources
                print("Checking for additional resources...")
                
                # Take a screenshot for reference
                screenshot_path = self.output_dir / "screenshot.png"
                await page.screenshot(path=screenshot_path, full_page=True)
                print(f"Screenshot saved: {screenshot_path}")
                
            except Exception as e:
                print(f"Error during scraping: {e}")
            finally:
                await browser.close()
        
        print(f"\nScraping completed! Files saved to: {self.output_dir}")
        print(f"Total files downloaded: {len(self.downloaded_files)}")
        
        # Create a simple server script
        self.create_server_script()
    
    def create_server_script(self):
        """Create a simple HTTP server script to serve the scraped content"""
        server_script = '''#!/usr/bin/env python3
"""
Simple HTTP server to serve the scraped website
Run: python server.py
Then open: http://localhost:8000
"""

import http.server
import socketserver
import os
import webbrowser

PORT = 8000

class MyHTTPRequestHandler(http.server.SimpleHTTPRequestHandler):
    def end_headers(self):
        self.send_header('Cache-Control', 'no-cache, no-store, must-revalidate')
        self.send_header('Pragma', 'no-cache')
        self.send_header('Expires', '0')
        super().end_headers()

if __name__ == "__main__":
    os.chdir(os.path.dirname(os.path.abspath(__file__)))
    
    with socketserver.TCPServer(("", PORT), MyHTTPRequestHandler) as httpd:
        print(f"Serving scraped website at http://localhost:{PORT}")
        print("Press Ctrl+C to stop the server")
        
        # Try to open browser automatically
        try:
            webbrowser.open(f'http://localhost:{PORT}')
        except:
            pass
            
        httpd.serve_forever()
'''
        
        server_file = self.output_dir / "server.py"
        with open(server_file, 'w', encoding='utf-8') as f:
            f.write(server_script)
        
        # Make it executable on Unix systems
        try:
            os.chmod(server_file, 0o755)
        except:
            pass
        
        print(f"Server script created: {server_file}")

async def main():
    """Main function"""
    url = "https://ohmyking.github.io/home/"
    scraper = WebScraper(url, "scraped_ohmyking_home")
    await scraper.scrape_website()

if __name__ == "__main__":
    asyncio.run(main())
