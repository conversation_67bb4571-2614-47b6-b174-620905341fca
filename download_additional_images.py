#!/usr/bin/env python3
"""
Download additional missing WebP images for wodniack.dev
"""

import requests
import os
from pathlib import Path
from urllib.parse import urljoin

def download_additional_images():
    """Download the additional missing WebP images"""
    
    base_url = "https://wodniack.dev/"
    output_dir = Path("_astro")
    
    # Additional missing WebP images from the latest 404 errors
    additional_images = [
        "setup-2016.DZszJSwz_10aiku.webp",
        "setup-2020.DjuS52Ke_1lqvvK.webp",
        "waaark.C5QpwSMH_Rq3S9.webp",
        "portfolio-2011.DpFoQfUQ_ZXEwJ9.webp",
        "portfolio-2014.ClRt5L9z_Xz3cl.webp",
        "portfolio-2017.N-r3CKDK_iyXU7.webp",
        "portfolio-2021.D5EtPDWp_Z1AfsSn.webp",
        "legos.Bdikeciy_Z1pUyVv.webp"
    ]
    
    print("🖼️  Downloading additional missing WebP images...")
    print("=" * 50)
    
    session = requests.Session()
    session.headers.update({
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
    })
    
    success_count = 0
    
    for i, image_file in enumerate(additional_images, 1):
        print(f"[{i}/{len(additional_images)}] Downloading: {image_file}")
        
        url = urljoin(base_url, f"_astro/{image_file}")
        local_path = output_dir / image_file
        
        try:
            response = session.get(url, timeout=30)
            response.raise_for_status()
            
            with open(local_path, 'wb') as f:
                f.write(response.content)
            
            print(f"✓ Saved: {local_path}")
            success_count += 1
            
        except Exception as e:
            print(f"✗ Failed to download {image_file}: {e}")
    
    print("=" * 50)
    print(f"Additional image download completed!")
    print(f"Success: {success_count}/{len(additional_images)}")
    
    if success_count == len(additional_images):
        print("🎉 All additional images downloaded successfully!")
        print("The website should now display all images properly.")
    else:
        print("⚠️  Some images failed to download.")
        print("The website will still work, but some images may not display.")

if __name__ == "__main__":
    download_additional_images()
