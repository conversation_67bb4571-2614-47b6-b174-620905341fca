# Web Scraper for OhMyKing's Home Page

这个爬虫专门用于抓取 https://ohmyking.github.io/home/<USER>

## 特性

- ✅ 支持JavaScript渲染的动态网页
- ✅ 自动下载所有相关资源（CSS、JS、图片、字体等）
- ✅ 保持相对路径结构，确保本地可正常运行
- ✅ 处理CSS中的嵌入资源
- ✅ 生成本地服务器脚本便于预览
- ✅ 自动截图保存页面效果

## 快速开始

### 方法1：一键运行（推荐）

```bash
python setup_and_run.py
```

这个脚本会自动：
1. 安装所需依赖
2. 安装Playwright浏览器
3. 运行爬虫
4. 启动本地服务器预览结果

### 方法2：手动运行

1. **安装依赖**
```bash
pip install -r requirements.txt
```

2. **安装Playwright浏览器**
```bash
python -m playwright install chromium
```

3. **运行爬虫**
```bash
python web_scraper.py
```

4. **预览结果**
```bash
cd scraped_ohmyking_home
python server.py
```

然后在浏览器中打开 http://localhost:8000

## 输出结构

爬虫会创建以下目录结构：

```
scraped_ohmyking_home/
├── index.html          # 主页面文件
├── server.py           # 本地服务器脚本
├── screenshot.png      # 页面截图
├── css/               # CSS文件
├── js/                # JavaScript文件
├── images/            # 图片文件
├── fonts/             # 字体文件
└── assets/            # 其他资源文件
```

## 技术实现

- **Playwright**: 处理JavaScript渲染和动态内容
- **BeautifulSoup**: HTML解析和处理
- **Requests**: 资源文件下载
- **异步处理**: 提高爬取效率

## 配置选项

可以在 `web_scraper.py` 中修改以下配置：

```python
# 修改目标URL
url = "https://ohmyking.github.io/home/"

# 修改输出目录
scraper = WebScraper(url, "your_output_directory")

# 修改浏览器模式（True为无头模式）
browser = await p.chromium.launch(headless=True)
```

## 注意事项

1. **网络连接**: 确保网络连接稳定，爬取过程需要下载多个资源文件
2. **磁盘空间**: 确保有足够的磁盘空间存储所有资源
3. **防火墙**: 某些防火墙可能会阻止Playwright下载浏览器
4. **Three.js**: 该网站使用了Three.js，爬虫会完整保存所有相关文件

## 故障排除

### 常见问题

1. **Playwright安装失败**
```bash
# 手动安装
python -m playwright install-deps
python -m playwright install chromium
```

2. **权限错误**
```bash
# Windows
python -m pip install --user -r requirements.txt

# Linux/Mac
sudo python3 -m pip install -r requirements.txt
```

3. **网络超时**
- 检查网络连接
- 尝试使用VPN
- 增加超时时间（在代码中修改timeout参数）

4. **资源下载失败**
- 某些资源可能由于网络问题下载失败
- 爬虫会继续运行并使用原始URL作为备选

## 自定义扩展

如果你想爬取其他网站，可以修改以下部分：

1. 修改 `main()` 函数中的URL
2. 根据目标网站调整等待时间
3. 添加特定的资源处理逻辑

## 许可证

本项目仅用于学习和研究目的。请遵守目标网站的robots.txt和使用条款。

## 支持

如果遇到问题，请检查：
1. Python版本（建议3.8+）
2. 网络连接
3. 防火墙设置
4. 磁盘空间
