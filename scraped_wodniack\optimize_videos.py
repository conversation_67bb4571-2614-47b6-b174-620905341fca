#!/usr/bin/env python3
"""
Script to optimize all video tags in the HTML file for better performance
"""

import re
import os

def optimize_video_tags(html_content):
    """
    Add preload="none" and loading="lazy" to all video tags
    """
    # Pattern to match video tags
    video_pattern = r'<video([^>]*?)(?<!preload="none")(?<!loading="lazy")>'
    
    def add_attributes(match):
        attributes = match.group(1)
        # Check if preload and loading attributes already exist
        if 'preload=' not in attributes:
            attributes += ' preload="none"'
        if 'loading=' not in attributes:
            attributes += ' loading="lazy"'
        return f'<video{attributes}>'
    
    # Replace all video tags
    optimized_content = re.sub(video_pattern, add_attributes, html_content)
    
    return optimized_content

def main():
    html_file = 'index.html'
    
    if not os.path.exists(html_file):
        print(f"❌ {html_file} not found!")
        return
    
    print(f"🔧 Optimizing video tags in {html_file}...")
    
    # Read the HTML file
    with open(html_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Count original video tags
    original_videos = len(re.findall(r'<video[^>]*>', content))
    
    # Optimize video tags
    optimized_content = optimize_video_tags(content)
    
    # Count optimized video tags
    optimized_videos = len(re.findall(r'<video[^>]*preload="none"[^>]*>', optimized_content))
    
    # Write back to file
    with open(html_file, 'w', encoding='utf-8') as f:
        f.write(optimized_content)
    
    print(f"✅ Optimization complete!")
    print(f"   📹 Total videos found: {original_videos}")
    print(f"   ⚡ Videos optimized: {optimized_videos}")
    print(f"   🎯 Added preload='none' and loading='lazy' attributes")

if __name__ == "__main__":
    main()
