#!/usr/bin/env python3
"""
Download missing files for the scraped website
"""

import requests
import os
from pathlib import Path

def download_file(url, local_path):
    """Download a file from URL to local path"""
    try:
        print(f"Downloading: {url}")
        response = requests.get(url, timeout=30)
        response.raise_for_status()
        
        # Create directory if it doesn't exist
        os.makedirs(os.path.dirname(local_path), exist_ok=True)
        
        with open(local_path, 'wb') as f:
            f.write(response.content)
        
        print(f"✓ Saved: {local_path}")
        return True
    except Exception as e:
        print(f"✗ Failed to download {url}: {e}")
        return False

def main():
    """Download all missing files"""
    base_url = "https://ohmyking.github.io/home/<USER>/"
    output_dir = Path("scraped_ohmyking_home")
    
    # List of missing files based on the network requests we saw earlier
    missing_files = [
        # Modules
        ("modules/renderer.js", "js/modules/renderer.js"),
        ("modules/Maf.js", "js/modules/Maf.js"),
        ("modules/post.js", "js/modules/post.js"),
        ("modules/fbo.js", "js/modules/fbo.js"),
        ("modules/ShaderPass.js", "js/modules/ShaderPass.js"),
        ("modules/bloomPass.js", "js/modules/bloomPass.js"),
        ("modules/ShaderPingPongPass.js", "js/modules/ShaderPingPongPass.js"),
        
        # Third party
        ("third_party/perlin.js", "js/third_party/perlin.js"),
        ("third_party/three.module.js", "js/third_party/three.module.js"),
        ("third_party/OrbitControls.js", "js/third_party/OrbitControls.js"),
        
        # Shaders
        ("shaders/ortho.js", "js/shaders/ortho.js"),
        ("shaders/vignette.js", "js/shaders/vignette.js"),
        ("shaders/noise.js", "js/shaders/noise.js"),
        ("shaders/screen.js", "js/shaders/screen.js"),
        ("shaders/blur.js", "js/shaders/blur.js"),
        ("shaders/fast-separable-gaussian-blur.js", "js/shaders/fast-separable-gaussian-blur.js"),
        
        # Images
        ("imgs/card1.png", "images/card1.png"),
    ]
    
    print("Downloading missing files...")
    print("=" * 50)
    
    success_count = 0
    total_count = len(missing_files)
    
    for remote_path, local_path in missing_files:
        url = base_url + remote_path
        full_local_path = output_dir / local_path
        
        if download_file(url, full_local_path):
            success_count += 1
    
    print("=" * 50)
    print(f"Download completed!")
    print(f"Success: {success_count}/{total_count}")
    
    # Now fix the import paths in the JavaScript files
    print("\nFixing import paths in JavaScript files...")
    fix_import_paths(output_dir)

def fix_import_paths(output_dir):
    """Fix import paths in JavaScript files"""
    js_files = [
        "js/page4.js",
        "js/page5.js", 
        "js/page6.js",
        "js/page7.js"
    ]
    
    for js_file in js_files:
        file_path = output_dir / js_file
        if file_path.exists():
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # Fix import paths
                content = content.replace('../modules/', './modules/')
                content = content.replace('../third_party/', './third_party/')
                content = content.replace('../shaders/', './shaders/')
                
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                
                print(f"✓ Fixed imports in: {js_file}")
            except Exception as e:
                print(f"✗ Failed to fix imports in {js_file}: {e}")

if __name__ == "__main__":
    main()
