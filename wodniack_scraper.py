#!/usr/bin/env python3
"""
Web Scraper for wodniack.dev
A creative developer's portfolio with unique binary/ASCII art design
"""

import os
import re
import asyncio
import requests
from urllib.parse import urljoin, urlparse, unquote
from pathlib import Path
from playwright.async_api import async_playwright
from bs4 import BeautifulSoup
import json

class WodniackScraper:
    def __init__(self, base_url="https://wodniack.dev/", output_dir="scraped_wodniack"):
        self.base_url = base_url
        self.output_dir = Path(output_dir)
        self.downloaded_files = set()
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        })
        
    def create_directories(self):
        """Create output directory structure"""
        dirs = [
            self.output_dir,
            self.output_dir / "fonts",
            self.output_dir / "_astro",
            self.output_dir / "images", 
            self.output_dir / "icons",
            self.output_dir / "cdn-cgi" / "scripts" / "5c5dd728" / "cloudflare-static",
            self.output_dir / "videos"
        ]
        
        for dir_path in dirs:
            dir_path.mkdir(parents=True, exist_ok=True)
    
    def sanitize_filename(self, filename):
        """Sanitize filename for filesystem"""
        filename = re.sub(r'[<>:"/\\|?*]', '_', filename)
        filename = re.sub(r'[^\w\-_\.]', '_', filename)
        return filename[:100]
    
    def download_resource(self, url, local_path):
        """Download a resource and return success status"""
        if url in self.downloaded_files:
            return True
            
        try:
            print(f"Downloading: {url}")
            response = self.session.get(url, timeout=60, stream=True)
            response.raise_for_status()
            
            # Create directory if it doesn't exist
            local_path.parent.mkdir(parents=True, exist_ok=True)
            
            # Download with progress for large files
            total_size = int(response.headers.get('content-length', 0))
            downloaded = 0
            
            with open(local_path, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    if chunk:
                        f.write(chunk)
                        downloaded += len(chunk)
                        if total_size > 0:
                            progress = (downloaded / total_size) * 100
                            if downloaded % (1024 * 1024) == 0:  # Print every MB
                                print(f"  Progress: {progress:.1f}%")
            
            self.downloaded_files.add(url)
            print(f"✓ Saved: {local_path}")
            return True
            
        except Exception as e:
            print(f"✗ Failed to download {url}: {e}")
            return False
    
    async def scrape_website(self):
        """Main scraping function"""
        print(f"Starting to scrape: {self.base_url}")
        self.create_directories()
        
        async with async_playwright() as p:
            browser = await p.chromium.launch(headless=False)
            page = await browser.new_page()
            
            try:
                print("Loading page...")
                await page.goto(self.base_url, wait_until="networkidle", timeout=60000)
                
                print("Waiting for content to load...")
                await page.wait_for_timeout(5000)
                
                # Get page content
                html_content = await page.content()
                
                # Parse HTML
                soup = BeautifulSoup(html_content, 'html.parser')
                
                print("Processing resources...")
                
                # Define resource mappings based on network requests
                resources = [
                    # Fonts
                    ("fonts/PPEditorialNew-Regular.woff2", "fonts/PPEditorialNew-Regular.woff2"),
                    ("fonts/PPEditorialNew-Ultralight.woff2", "fonts/PPEditorialNew-Ultralight.woff2"),
                    ("fonts/PPFraktionMono-Regular.woff2", "fonts/PPFraktionMono-Regular.woff2"),
                    ("fonts/PPFraktionMono-Bold.woff2", "fonts/PPFraktionMono-Bold.woff2"),
                    ("fonts/Bigger-Display.woff2", "fonts/Bigger-Display.woff2"),
                    
                    # CSS and JS
                    ("_astro/index.0nGmL9XR.css", "_astro/index.0nGmL9XR.css"),
                    ("_astro/hoisted.CFlnv3Zw.js", "_astro/hoisted.CFlnv3Zw.js"),
                    
                    # Images and Icons
                    ("images/qr-code.svg", "images/qr-code.svg"),
                    ("images/asset-star.svg", "images/asset-star.svg"),
                    ("images/sprite-vanish.png", "images/sprite-vanish.png"),
                    ("images/asset-smiley--main.svg", "images/asset-smiley--main.svg"),
                    ("images/asset-smiley--contrasted.svg", "images/asset-smiley--contrasted.svg"),
                    ("icons/favicon.svg", "icons/favicon.svg"),
                    
                    # Cloudflare script
                    ("cdn-cgi/scripts/5c5dd728/cloudflare-static/email-decode.min.js", 
                     "cdn-cgi/scripts/5c5dd728/cloudflare-static/email-decode.min.js"),
                ]
                
                # Download basic resources
                success_count = 0
                for remote_path, local_path in resources:
                    url = urljoin(self.base_url, remote_path)
                    full_local_path = self.output_dir / local_path
                    if self.download_resource(url, full_local_path):
                        success_count += 1
                
                # Download video files (these are large, so we'll be selective)
                video_files = [
                    "_astro/Agences-Work.CcLpoyOs.mp4",
                    "_astro/Nod-Transition.Cgjsr7Jp.mp4",
                    "_astro/Incredibles-Scroll.C2FsGtmZ.mp4",
                    "_astro/Deside-Site.CmhdHL0t.mp4",
                    "_astro/Duten-3D.CnijAj5p.mp4",
                    "_astro/Pen-4.BOCLYASq.mp4",
                    "_astro/Pt-Navigation.GbcOoFQt.mp4",
                    "_astro/Nod-404.DvJptcy9.mp4",
                    "_astro/Hf-Navigation.Gkvt7Etn.mp4",
                    "_astro/Generous-Transition.Dysz98iA.mp4",
                    "_astro/Rudl-Transition.CcgAkF6S.mp4",
                    "_astro/Pen-5.CI9l33_j.mp4",
                    "_astro/Duten-Fluid.t8C3AKXP.mp4",
                    "_astro/Duten-Intro.CP3CW1O6.mp4",
                    "_astro/Qbit-Menu.DQBETGn4.mp4",
                    "_astro/HS-Site.Br4spn2E.mp4",
                    "_astro/Duten-Products.D0b17Eb7.mp4",
                    "_astro/Pen-6.CMyHVvwm.mp4",
                    "_astro/Incredibles-Ray.lxZWWm77.mp4",
                    "_astro/Rudl-Scroll.DmL08CDl.mp4",
                    "_astro/Generous-Scroll1.0UenKQ_7.mp4",
                    "_astro/Caxis-Transition.BHJmdqmC.mp4",
                    "_astro/Nod-Links.De8GBDpL.mp4",
                    "_astro/Pen-7.BzyGmIZt.mp4",
                    "_astro/Generous-Slider.SVzdHB83.mp4",
                    "_astro/Duten-Shapes.DvaTdo7l.mp4",
                    "_astro/Nod-Flower.VBa6NQlZ.mp4",
                    "_astro/Qbit-Shape.BySbOvpY.mp4",
                    "_astro/Nod-Intro.DujLdLjK.mp4",
                    "_astro/Pen-8.G-raKJSF.mp4",
                    "_astro/Kohost-Intro.CA4nMYt7.mp4",
                    "_astro/Saniswiss-Products.D7GK9AEH.mp4",
                    "_astro/Hapu-Interaction.CqeTD_4p.mp4"
                ]
                
                print(f"\nDownloading {len(video_files)} video files (this may take a while)...")
                video_success = 0
                for i, video_file in enumerate(video_files, 1):
                    print(f"\n[{i}/{len(video_files)}] Processing video: {video_file}")
                    url = urljoin(self.base_url, video_file)
                    local_path = self.output_dir / video_file
                    if self.download_resource(url, local_path):
                        video_success += 1
                
                # Save the main HTML file
                html_file = self.output_dir / "index.html"
                with open(html_file, 'w', encoding='utf-8') as f:
                    f.write(str(soup))
                
                print(f"Main HTML saved: {html_file}")
                
                # Take a screenshot
                screenshot_path = self.output_dir / "screenshot.png"
                await page.screenshot(path=screenshot_path, full_page=True)
                print(f"Screenshot saved: {screenshot_path}")
                
            except Exception as e:
                print(f"Error during scraping: {e}")
            finally:
                await browser.close()
        
        print(f"\n{'='*60}")
        print(f"Scraping completed!")
        print(f"{'='*60}")
        print(f"Output directory: {self.output_dir}")
        print(f"Basic resources downloaded: {success_count}/{len(resources)}")
        print(f"Video files downloaded: {video_success}/{len(video_files)}")
        print(f"Total files downloaded: {len(self.downloaded_files)}")
        
        # Create server script
        self.create_server_script()
        
        # Create analysis document
        self.create_analysis_document()
    
    def create_server_script(self):
        """Create a simple HTTP server script"""
        server_script = '''#!/usr/bin/env python3
"""
Simple HTTP server for the scraped wodniack.dev website
Run: python server.py
Then open: http://localhost:8000
"""

import http.server
import socketserver
import os
import webbrowser

PORT = 8000

class MyHTTPRequestHandler(http.server.SimpleHTTPRequestHandler):
    def end_headers(self):
        self.send_header('Cache-Control', 'no-cache, no-store, must-revalidate')
        self.send_header('Pragma', 'no-cache')
        self.send_header('Expires', '0')
        super().end_headers()

if __name__ == "__main__":
    os.chdir(os.path.dirname(os.path.abspath(__file__)))
    
    with socketserver.TCPServer(("", PORT), MyHTTPRequestHandler) as httpd:
        print(f"Serving scraped wodniack.dev at http://localhost:{PORT}")
        print("Press Ctrl+C to stop the server")
        
        try:
            webbrowser.open(f'http://localhost:{PORT}')
        except:
            pass
            
        httpd.serve_forever()
'''
        
        server_file = self.output_dir / "server.py"
        with open(server_file, 'w', encoding='utf-8') as f:
            f.write(server_script)
        
        try:
            os.chmod(server_file, 0o755)
        except:
            pass
        
        print(f"Server script created: {server_file}")

    def create_analysis_document(self):
        """Create a technical analysis document"""
        analysis = f'''# Wodniack.dev - Technical Analysis

## Project Overview

**Website**: https://wodniack.dev/
**Developer**: Antoine Wodniack
**Type**: Creative Developer Portfolio
**Framework**: Astro (Static Site Generator)

## Design Characteristics

### Visual Style
- **Binary/ASCII Art Theme**: Extensive use of binary code (01) patterns as decorative elements
- **Monospace Typography**: Heavy use of monospace fonts for the tech aesthetic
- **Minimalist Layout**: Clean, structured design with plenty of white space
- **High Contrast**: Strong black and white contrast with selective color usage

### Typography
- **PPEditorialNew-Regular.woff2**: Main body text font
- **PPEditorialNew-Ultralight.woff2**: Light weight variant
- **PPFraktionMono-Regular.woff2**: Monospace font for code/binary elements
- **PPFraktionMono-Bold.woff2**: Bold monospace variant
- **Bigger-Display.woff2**: Display/heading font

### Interactive Elements
- **Video Showcases**: {len([f for f in os.listdir(self.output_dir / "_astro") if f.endswith('.mp4')]) if (self.output_dir / "_astro").exists() else 0} video files showcasing work
- **Smooth Animations**: CSS/JS powered transitions and effects
- **Responsive Design**: Mobile-first approach

## Technical Stack

### Frontend Framework
- **Astro**: Modern static site generator
- **CSS**: Custom styling with modern features
- **JavaScript**: Interactive elements and animations

### Assets
- **Fonts**: 5 custom web fonts (WOFF2 format)
- **Images**: SVG icons and graphics
- **Videos**: MP4 format for project showcases
- **Icons**: Custom favicon and UI elements

### Performance Features
- **Static Generation**: Pre-built HTML for fast loading
- **Optimized Assets**: Compressed fonts and images
- **Lazy Loading**: Videos load on demand
- **CDN Integration**: Cloudflare for global delivery

## File Structure

```
scraped_wodniack/
├── index.html                 # Main page
├── server.py                  # Local development server
├── screenshot.png             # Full page screenshot
├── fonts/                     # Custom web fonts
│   ├── PPEditorialNew-Regular.woff2
│   ├── PPEditorialNew-Ultralight.woff2
│   ├── PPFraktionMono-Regular.woff2
│   ├── PPFraktionMono-Bold.woff2
│   └── Bigger-Display.woff2
├── _astro/                    # Astro generated assets
│   ├── index.0nGmL9XR.css    # Main stylesheet
│   ├── hoisted.CFlnv3Zw.js   # JavaScript bundle
│   └── *.mp4                 # Project showcase videos
├── images/                    # Graphics and icons
│   ├── qr-code.svg
│   ├── asset-star.svg
│   ├── sprite-vanish.png
│   └── asset-smiley-*.svg
├── icons/                     # Favicon and UI icons
└── cdn-cgi/                   # Cloudflare scripts
```

## Key Features

### Creative Elements
1. **Binary Art**: Decorative binary code patterns throughout the design
2. **Typography Mixing**: Combination of editorial and monospace fonts
3. **Video Portfolio**: Rich media showcasing of development work
4. **Interactive Animations**: Smooth transitions and hover effects

### Technical Excellence
1. **Performance Optimized**: Fast loading with optimized assets
2. **Accessibility**: Proper semantic HTML structure
3. **SEO Friendly**: Meta tags and structured content
4. **Mobile Responsive**: Adaptive design for all devices

## Awards and Recognition

Based on the content, Antoine Wodniack has received numerous awards:
- **Awwwards**: SOTD x 16, Honors x 1
- **FWA**: SOTD x 4, MOTD x 2
- **CSSDA**: WOTD x 18, WOTM x 1
- **2025 Webby Awards Winner**: Best Home Page
- **GSAP SOTM**: October & November 2024
- **CSSDA Best Front-End Developer**: 2015 & 2016

## Development Insights

### For Similar Projects
1. **Use Astro**: Great for performance-focused portfolios
2. **Custom Fonts**: Invest in unique typography for brand identity
3. **Video Content**: Showcase work with high-quality video demos
4. **Binary/Code Aesthetic**: Appeals to developer audience
5. **Performance First**: Optimize all assets for fast loading

### Technical Recommendations
- Implement lazy loading for videos
- Use modern image formats (WebP, AVIF)
- Optimize font loading with font-display: swap
- Consider dark/light theme toggle
- Add progressive enhancement for animations

---

**Scraped on**: {__import__('datetime').datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
**Total Files**: {len(self.downloaded_files)}
**Status**: {'Complete' if len(self.downloaded_files) > 30 else 'Partial'}
'''
        
        analysis_file = self.output_dir / "TECHNICAL_ANALYSIS.md"
        with open(analysis_file, 'w', encoding='utf-8') as f:
            f.write(analysis)
        
        print(f"Technical analysis created: {analysis_file}")

async def main():
    """Main function"""
    scraper = WodniackScraper()
    await scraper.scrape_website()

if __name__ == "__main__":
    asyncio.run(main())
