
/* PERFORMANCE OPTIMIZED styles for wodniack.dev */

/* Disable expensive animations and transitions for better performance */
.js-char, [class*="char"] {
    /* Remove expensive transitions that cause lag */
    transition: none !important;
    will-change: auto !important;
    transform: none !important;
}

/* Simplified hover effects - only for visible elements */
.js-char:hover, [class*="char"]:hover {
    /* Lightweight color change instead of transform */
    color: #ff6b6b !important;
    transition: color 0.1s ease !important;
}

/* Disable drag interactions that cause severe lag */
.js-char, [class*="char"] {
    cursor: default !important;
    user-select: none !important;
    pointer-events: auto !important;
}

/* Optimize scrolling performance */
html {
    scroll-behavior: auto !important; /* Disable smooth scrolling for better performance */
}

body {
    overflow-x: hidden !important;
    /* Add hardware acceleration */
    transform: translateZ(0);
    backface-visibility: hidden;
}

/* Optimize sections for better rendering */
section, .section, [class*="section"] {
    position: relative !important;
    z-index: 1 !important;
    /* Add containment for better performance */
    contain: layout style paint;
}

/* Enhanced footer visibility */
footer, [role="contentinfo"] {
    position: relative !important;
    z-index: 2 !important;
    background: inherit !important;
}

/* Disable expensive animations */
@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.8; }
}

.coin-trigger {
    /* Use opacity instead of transform for better performance */
    animation: pulse 3s infinite;
}

/* Performance optimizations for video containers */
.s__scene__work--video {
    contain: layout style;
    will-change: auto;
}

/* Optimize canvas and SVG elements */
canvas, svg {
    will-change: auto;
    transform: translateZ(0);
}

/* Reduce motion for better performance */
* {
    animation-duration: 0.1s !important;
    transition-duration: 0.1s !important;
}
