<!DOCTYPE html>
<html class="astro-j7pv25f6 is-windows is-chrome lenis has-scrollbar is-loaded" lang="en"><head><meta charset="utf-8"/><title>AW - Creative Developer Freelance - France</title><meta content="Creative Developer with 15+ years and 140+ projects, specializing in animation-driven, high-impact websites. Partnering with designers to craft memorable UX." name="description"/><meta content="width=device-width" name="viewport"/><meta content="Astro v4.15.9" name="generator"/><link href="/icons/favicon-48x48.png" rel="icon" sizes="48x48" type="image/png"/><link href="/icons/favicon.svg" rel="icon" type="image/svg+xml"/><link href="/icons/favicon.ico" rel="shortcut icon"/><link href="/icons/apple-touch-icon.png" rel="apple-touch-icon" sizes="180x180"/><meta content="AW Dev" name="apple-mobile-web-app-title"/><meta content="#160000" name="theme-color"/><meta content="#160000" name="msapplication-navbutton-color"/><meta content="#160000" name="apple-mobile-web-app-status-bar-style"/><meta content="en_GB" property="og:locale"/><meta content="website" property="og:type"/><meta content="AW - Creative Developer Freelance - France" property="og:title"/><meta content="Creative Developer with 15+ years and 140+ projects, specializing in animation-driven, high-impact websites. Partnering with designers to craft memorable UX." property="og:description"/><meta content="https://wodniack.dev/" property="og:url"/><meta content="AW - Creative Developer Freelance - France" property="og:site_name"/><meta content="https://wodniack.dev/images/aw-creative-developer.png" property="og:image"/><meta content="1200" property="og:image:width"/><meta content="675" property="og:image:height"/><meta content="summary_large_image" name="twitter:card"/><meta content="https://wodniack.dev/images/aw-creative-developer.png" name="twitter:image"/><link as="font" crossorigin="anonymous" href="/fonts/PPEditorialNew-Regular.woff2" rel="preload" type="font/woff2"/><link as="font" crossorigin="anonymous" href="/fonts/PPEditorialNew-Ultralight.woff2" rel="preload" type="font/woff2"/><link as="font" crossorigin="anonymous" href="/fonts/PPFraktionMono-Regular.woff2" rel="preload" type="font/woff2"/><link as="font" crossorigin="anonymous" href="/fonts/PPFraktionMono-Bold.woff2" rel="preload" type="font/woff2"/><link as="font" crossorigin="anonymous" href="/fonts/Bigger-Display.woff2" rel="preload" type="font/woff2"/><link href="/_astro/index.0nGmL9XR.css" rel="stylesheet"/><script src="/_astro/hoisted.CFlnv3Zw.js" type="module"></script>

<script>
// Enhanced error handling and interaction restoration for wodniack.dev
(function() {
    'use strict';
    
    console.log('🚀 Initializing enhanced wodniack.dev experience...');
    
    // 1. Suppress known errors
    const originalError = console.error;
    console.error = function(...args) {
        const message = args.join(' ');
        if (message.includes('querySelectorAll') || 
            message.includes('favicon.ico') ||
            message.includes('content-visibility') ||
            message.includes('Cannot read properties of null')) {
            return; // Suppress these errors
        }
        originalError.apply(console, args);
    };
    
    // 2. Enhanced DOM element creation
    function createMissingElements() {
        const missingElements = [
            { tag: 'div', id: 'work-section', class: 'work-container' },
            { tag: 'div', id: 'about-section', class: 'about-container' },
            { tag: 'div', id: 'contact-section', class: 'contact-container' },
            { tag: 'div', id: 'intro-section', class: 'intro-container' },
            { tag: 'div', id: 'awards-section', class: 'awards-container' }
        ];
        
        missingElements.forEach(elem => {
            if (!document.getElementById(elem.id)) {
                const element = document.createElement(elem.tag);
                element.id = elem.id;
                if (elem.class) element.className = elem.class;
                element.style.display = 'none';
                document.body.appendChild(element);
            }
        });
    }
    
    // 3. Binary grid drag functionality simulation
    function initBinaryGridInteraction() {
        const binaryChars = document.querySelectorAll('.js-char, [class*="char"]');
        console.log(`Found ${binaryChars.length} binary characters`);
        
        binaryChars.forEach(char => {
            if (char.textContent.includes('0') || char.textContent.includes('1')) {
                char.style.cursor = 'grab';
                char.style.userSelect = 'none';
                char.style.transition = 'all 0.2s ease';
                
                let isDragging = false;
                let startX, startY, startLeft, startTop;
                
                char.addEventListener('mousedown', (e) => {
                    isDragging = true;
                    char.style.cursor = 'grabbing';
                    char.style.zIndex = '1000';
                    char.style.position = 'relative';
                    
                    startX = e.clientX;
                    startY = e.clientY;
                    startLeft = parseInt(char.style.left) || 0;
                    startTop = parseInt(char.style.top) || 0;
                    
                    e.preventDefault();
                });
                
                document.addEventListener('mousemove', (e) => {
                    if (!isDragging) return;
                    
                    const deltaX = e.clientX - startX;
                    const deltaY = e.clientY - startY;
                    
                    char.style.left = (startLeft + deltaX) + 'px';
                    char.style.top = (startTop + deltaY) + 'px';
                    char.style.transform = `rotate(${deltaX * 0.1}deg)`;
                });
                
                document.addEventListener('mouseup', () => {
                    if (isDragging) {
                        isDragging = false;
                        char.style.cursor = 'grab';
                        
                        // Animate back to original position
                        setTimeout(() => {
                            char.style.transition = 'all 0.5s ease';
                            char.style.left = '0px';
                            char.style.top = '0px';
                            char.style.transform = 'rotate(0deg)';
                            char.style.zIndex = 'auto';
                        }, 100);
                    }
                });
                
                // Hover effects
                char.addEventListener('mouseenter', () => {
                    if (!isDragging) {
                        char.style.transform = 'scale(1.1)';
                        char.style.color = '#ff6b6b';
                    }
                });
                
                char.addEventListener('mouseleave', () => {
                    if (!isDragging) {
                        char.style.transform = 'scale(1)';
                        char.style.color = '';
                    }
                });
            }
        });
    }
    
    // 4. Coin drop animation simulation
    function initCoinDropAnimation() {
        let coinCount = 0;
        
        function createCoin() {
            const coin = document.createElement('div');
            coin.innerHTML = '💰';
            coin.style.cssText = `
                position: fixed;
                top: -50px;
                left: ${Math.random() * window.innerWidth}px;
                font-size: 24px;
                z-index: 999;
                pointer-events: none;
                animation: coinDrop 3s linear forwards;
            `;
            
            document.body.appendChild(coin);
            coinCount++;
            
            setTimeout(() => {
                if (coin.parentNode) {
                    coin.parentNode.removeChild(coin);
                }
            }, 3000);
        }
        
        // Add CSS animation for coin drop
        const style = document.createElement('style');
        style.textContent = `
            @keyframes coinDrop {
                0% {
                    transform: translateY(-50px) rotate(0deg);
                    opacity: 1;
                }
                100% {
                    transform: translateY(${window.innerHeight + 50}px) rotate(360deg);
                    opacity: 0;
                }
            }
        `;
        document.head.appendChild(style);
        
        // Trigger coin drops on scroll
        let lastScrollY = window.scrollY;
        window.addEventListener('scroll', () => {
            const currentScrollY = window.scrollY;
            const scrollDelta = Math.abs(currentScrollY - lastScrollY);
            
            if (scrollDelta > 100 && coinCount < 5) {
                createCoin();
                if (Math.random() > 0.7) createCoin(); // Sometimes drop two coins
            }
            
            lastScrollY = currentScrollY;
        });
        
        console.log('💰 Coin drop animation initialized');
    }
    
    // 5. Enhanced scroll effects
    function initScrollEffects() {
        const sections = document.querySelectorAll('section, .section, [class*="section"]');
        
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.style.opacity = '1';
                    entry.target.style.transform = 'translateY(0)';
                }
            });
        }, { threshold: 0.1 });
        
        sections.forEach(section => {
            section.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
            section.style.opacity = '0.8';
            section.style.transform = 'translateY(20px)';
            observer.observe(section);
        });
    }
    
    // 6. Fix display issues
    function fixDisplayIssues() {
        // Ensure all content is visible
        const hiddenElements = document.querySelectorAll('[style*="display: none"]');
        hiddenElements.forEach(el => {
            if (!el.id.includes('placeholder') && !el.className.includes('hidden')) {
                el.style.display = '';
            }
        });
        
        // Fix potential z-index issues
        const footer = document.querySelector('footer, [role="contentinfo"]');
        if (footer) {
            footer.style.position = 'relative';
            footer.style.zIndex = '1';
        }
        
        // Ensure proper scrolling
        document.body.style.overflowX = 'hidden';
        document.documentElement.style.scrollBehavior = 'smooth';
    }
    
    // 7. Initialize everything when DOM is ready
    function initialize() {
        createMissingElements();
        initBinaryGridInteraction();
        initCoinDropAnimation();
        initScrollEffects();
        fixDisplayIssues();
        
        console.log('✅ Enhanced wodniack.dev experience initialized!');
        console.log('🎮 Try dragging the binary numbers!');
        console.log('💰 Scroll to see coin drops!');
    }
    
    // Wait for DOM and run initialization
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initialize);
    } else {
        initialize();
    }
    
    // Re-initialize after any dynamic content loads
    setTimeout(initialize, 2000);
    
})();
</script>

<link rel="stylesheet" href="enhanced_interactions.css">
</head> <body class="astro-j7pv25f6" style=""> <div class="site-wrapper js-site-wrapper astro-j7pv25f6" style=""> <header class="site-head astro-5qrshpxv is-in-view" data-intersect="" style="opacity: 1; translate: none; rotate: none; scale: none; transform: translate(0px, 0%);"> <div class="site-head__container astro-5qrshpxv"> <div class="sb-logo astro-5qrshpxv"> <a class="astro-5qrshpxv" href="/"> <svg class="js-logo astro-5qrshpxv" fill="none" height="280" style="translate: none; rotate: none; scale: none; transform: translate(0px, 0%);" viewbox="0 0 280 280" width="280" xmlns="http://www.w3.org/2000/svg"> <path class="astro-5qrshpxv" d="M240.245 0v263.2h-19.894V0h-39.756v263.2h-19.861V0h-39.755v280H280V0h-39.755Z" fill="#160000"></path> <path class="astro-5qrshpxv" d="M0 0v280h39.755V16.8H59.65V280h39.756V0H0Z" fill="#160000"></path> </svg><span class="u-sr-only astro-5qrshpxv">Antoine Wodniack</span> </a> </div><!-- .sb-logo --> <div class="sb-console astro-5qrshpxv" role="presentation"> <div class="sb-console__inner js-console astro-5qrshpxv">
Almost </div><!-- .sb-console__inner --> </div><!-- .sb-console --> <nav class="sb-menu astro-5qrshpxv"> <ul class="sb__list astro-5qrshpxv"> <li class="sb__item js-menu-item astro-5qrshpxv" style="translate: none; rotate: none; scale: none; transform: translate(0px, 0%);"> <a class="js-menu-link astro-5qrshpxv" href="#about"> <span class="sb__text astro-5qrshpxv">About</span> </a> </li><li class="sb__item js-menu-item astro-5qrshpxv" style="translate: none; rotate: none; scale: none; transform: translate(0px, 0%);"> <a class="js-menu-link astro-5qrshpxv" href="#work"> <span class="sb__text astro-5qrshpxv">Work</span> </a> </li><li class="sb__item js-menu-item astro-5qrshpxv" style="translate: none; rotate: none; scale: none; transform: translate(0px, 0%);"> <a class="js-menu-link astro-5qrshpxv" href="#contact"> <span class="sb__text astro-5qrshpxv">Contact</span> </a> </li> </ul> </nav><!-- .sb-menu --> <ul class="sb-socials astro-5qrshpxv"> <li class="sb__item astro-5qrshpxv"> <a class="astro-5qrshpxv" href="https://codepen.io/wodniack" rel="noopener" target="_blank"> <span class="sb__icon sb__icon--codepen astro-5qrshpxv" style="--path: path('M19.3184 6.01751L10.4517 0.105976C10.3477 0.0365379 10.2254 -0.000518799 10.1002 -0.000518799C9.97513 -0.000518799 9.85282 0.0365379 9.74874 0.105976L0.882076 6.01751C0.795245 6.07529 0.724029 6.15362 0.674752 6.24555C0.625475 6.33747 0.599663 6.44014 0.599609 6.54444V12.456C0.599609 12.6675 0.706009 12.8651 0.882076 12.9829L9.74874 18.8944C9.85269 18.9636 9.97476 19.0005 10.0996 19.0005C10.2245 19.0005 10.3465 18.9636 10.4505 18.8944L19.3171 12.9829C19.404 12.9251 19.4752 12.8468 19.5245 12.7549C19.5737 12.6629 19.5996 12.5603 19.5996 12.456V6.54444C19.5996 6.44014 19.5737 6.33747 19.5245 6.24555C19.4752 6.15362 19.404 6.07529 19.3171 6.01751H19.3184ZM10.1009 11.6934L6.80881 9.49958L10.1009 7.30571L13.3929 9.49958L10.1009 11.6934ZM10.7342 6.20498V1.81598L17.8263 6.54318L14.5342 8.73704L10.7342 6.20371V6.20498ZM9.46754 6.20498L5.66754 8.73831L2.37548 6.54444L9.46754 1.81724V6.20498ZM4.52628 9.49958L1.86754 11.2716V7.72751L4.52628 9.49958ZM5.66754 10.2608L9.46754 12.7942V17.1832L2.37548 12.456L5.66754 10.2608ZM10.7342 12.7942L14.5342 10.2608L17.8263 12.4547L10.7342 17.1819V12.7942ZM15.6755 9.49958L18.3342 7.72751V11.2716L15.6755 9.49958Z');"></span> <span class="u-sr-only astro-5qrshpxv">Follow me on CodePen</span> </a> </li> <li class="sb__item astro-5qrshpxv"> <a class="astro-5qrshpxv" href="https://www.linkedin.com/in/wodniack/" rel="noopener" target="_blank"> <span class="sb__icon sb__icon--linkedin astro-5qrshpxv" style="--path: path('M1.13025 14.9839H3.93671V4.6H1.13025V14.9839ZM2.53348 0.0161285C1.598 0.0161285 0.849609 0.764516 0.849609 1.7C0.849609 2.63548 1.598 3.38387 2.53348 3.38387C3.46896 3.38387 4.21735 2.63548 4.21735 1.7C4.21735 0.764516 3.46896 0.0161285 2.53348 0.0161285ZM8.70767 6.19032V4.6H5.90122V14.9839H8.70767V9.65161C8.70767 6.65806 12.5432 6.47097 12.5432 9.65161V14.9839H15.3496V8.62258C15.3496 3.57097 10.0174 3.75806 8.70767 6.19032Z');"></span> <span class="u-sr-only astro-5qrshpxv">Follow me on LinkedIn</span> </a> </li> </ul><!-- .sb-socials --> <button class="sb-contrast js-contrast astro-5qrshpxv" type="button"> <span class="sb__icon astro-5qrshpxv" style="--path: path('M10.0996 20C8.71628 20 7.41628 19.7373 6.19961 19.212C4.98294 18.6867 3.92461 17.9743 3.02461 17.075C2.12461 16.1757 1.41228 15.1173 0.887611 13.9C0.362944 12.6827 0.100277 11.3827 0.0996106 10C0.098944 8.61733 0.361611 7.31733 0.887611 6.1C1.41361 4.88267 2.12594 3.82433 3.02461 2.925C3.92328 2.02567 4.98161 1.31333 6.19961 0.788C7.41761 0.262667 8.71761 0 10.0996 0C11.4816 0 12.7816 0.262667 13.9996 0.788C15.2176 1.31333 16.2759 2.02567 17.1746 2.925C18.0733 3.82433 18.7859 4.88267 19.3126 6.1C19.8393 7.31733 20.1016 8.61733 20.0996 10C20.0976 11.3827 19.8349 12.6827 19.3116 13.9C18.7883 15.1173 18.0759 16.1757 17.1746 17.075C16.2733 17.9743 15.2149 18.687 13.9996 19.213C12.7843 19.739 11.4843 20.0013 10.0996 20ZM11.0996 17.925C13.0829 17.675 14.7456 16.804 16.0876 15.312C17.4296 13.82 18.1003 12.0493 18.0996 10C18.0989 7.95067 17.4279 6.18 16.0866 4.688C14.7453 3.196 13.0829 2.325 11.0996 2.075V17.925Z');"></span> <span class="u-sr-only astro-5qrshpxv">Change contrast</span> </button><!-- .sb-contrast --> <aside class="sb-availability astro-5qrshpxv"> <p class="astro-5qrshpxv"> <span class="sb__line astro-5qrshpxv"> <span class="sb__text astro-5qrshpxv">Coding globally from France.</span> </span> <span class="sb__line astro-5qrshpxv"> <span class="sb__text astro-5qrshpxv">Available for freelance work →</span> <a class="sb__link astro-5qrshpxv" href="mailto:<EMAIL>">Hire me</a> </span> </p> </aside><!-- .sb-availability --> <a class="sb-qr-code js-qr-code astro-5qrshpxv" href="mailto:<EMAIL>" style="--bg-p: 100%;" title="Contact me!"> <img alt="QR Code" class="astro-5qrshpxv" height="72" src="/images/qr-code.svg" width="72"/> </a><!-- .sb-qr-code --> </div><!-- .site-head__container --> </header> <div class="s-hero astro-7dteeuzc is-in-view" data-intersect="" style="opacity: 1;"> <a-waves class="s__waves astro-7dteeuzc astro-opemy3db is-in-view" data-intersect="" style="--x: -9.999999999999993px; --y: 0px; translate: none; rotate: none; scale: none; transform: translate(0px, 0%);"> <svg class="js-svg astro-opemy3db" style="width: 1248px; height: 334px;"><path class="a__line js-line" d="M -73.7 -33.4L -73.7 -33.4L -69 7.7L -76.5 49.3L -96.7 86.9L -120.7 115.6L -132.9 136.3L -124.9 156.4L -104.4 183.1L -85.3 217.1L -74.6 254L -70.7 289.9L -70.1 322.9L -71.5 352.8"></path><path class="a__line js-line" d="M -60.5 -29.9L -60.5 -29.9L -60.3 11.5L -72.1 51.9L -94 86.9L -115.7 113.2L -122.8 133.1L -111 154.5L -90 183L -72.6 217.9L -63.5 254.8L -60.5 290.1L -60.3 322.5L -62.4 351.8"></path><path class="a__line js-line" d="M -49.1 -26.1L -49.1 -26.1L -53.3 15L -68.6 53.8L -91.2 86.2L -109.4 110.3L -111.5 130.1L -96.5 153L -75.7 183.2L -60.2 218.9L -52.7 255.6L -50.4 290.3L -50.6 322L -53.6 350.8"></path><path class="a__line js-line" d="M -39.5 -22.3L -39.5 -22.3L -47.9 18L -65.9 54.8L -87.7 84.6L -101.9 107.2L -99.1 127.3L -81.7 151.9L -61.6 183.7L -47.9 219.9L -41.9 256.3L -40.3 290.5L -40.9 321.5L -44.9 349.8"></path><path class="a__line js-line" d="M -31.6 -18.7L -31.6 -18.7L -43.7 20.5L -63.3 55L -83.4 82.4L -93 103.9L -85.7 124.8L -66.7 151.3L -47.8 184.4L -36 221L -31.3 257.1L -30.2 290.6L -31.3 321L -36.4 348.8"></path><path class="a__line js-line" d="M -25.3 -15.5L -25.3 -15.5L -40.3 22.1L -60.4 54.3L -77.8 79.8L -82.7 100.7L -71.6 122.8L -51.9 151L -34.4 185.3L -24.5 222L -20.8 257.7L -20.3 290.5L -21.9 320.3L -28.3 347.7"></path><path class="a__line js-line" d="M -20.1 -12.9L -20.1 -12.9L -37 22.9L -56.6 53L -70.7 76.9L -71.2 97.8L -57.4 121.3L -37.6 151.1L -21.8 186.2L -13.5 222.9L -10.6 258L -10.5 290.2L -12.9 319.3L -20.8 346.6"></path><path class="a__line js-line" d="M -15.4 -11L -15.4 -11L -33.4 23L -51.7 51.2L -62.4 74.1L -59.1 95.3L -43.3 120.2L -24.1 151.4L -9.9 187L -2.9 223.4L -0.7 257.9L -1 289.4L -4.4 318.1L -14.3 345.4"></path><path class="a__line js-line" d="M -10.8 -9.8L -10.8 -9.8L -29.1 22.5L -45.6 49.2L -53 71.5L -46.6 93.4L -29.8 119.6L -11.4 151.7L 1.3 187.5L 7.3 223.5L 9 257.4L 8 288.3L 3.2 316.6L -8.9 344.2"></path><path class="a__line js-line" d="M -5.8 -9.2L -5.8 -9.2L -23.9 21.6L -38.4 47.3L -42.8 69.4L -34.3 92L -17 119.3L 0.2 152L 11.7 187.7L 16.9 223.2L 18.1 256.4L 16.4 286.7L 9.8 314.8L -4.7 343.3"></path><path class="a__line js-line" d="M -0.2 -9L -0.2 -9L -17.6 20.7L -30.2 45.5L -32.3 67.7L -22.3 91.1L -5.2 119.1L 10.9 152.1L 21.3 187.5L 25.9 222.4L 26.6 254.9L 23.7 284.8L 14.9 313.1L -1.9 343"></path><path class="a__line js-line" d="M 6.2 -9.1L 6.2 -9.1L -10.4 19.7L -21.3 44.1L -21.7 66.5L -11 90.5L 5.5 119.1L 20.5 152.1L 30.1 187L 34.2 221.1L 34.1 253.1L 29.6 282.7L 18.4 311.7L 0 343.7"></path><path class="a__line js-line" d="M 13.5 -9.2L 13.5 -9.2L -2.4 18.9L -11.9 43.1L -11.2 65.7L -0.4 90.3L 15.2 119.1L 29.1 151.8L 37.8 186.1L 41.3 219.5L 40.2 251L 33.7 280.8L 20.2 311L 1.5 345.6"></path><path class="a__line js-line" d="M 21.6 -9.4L 21.6 -9.4L 6.5 18.4L -2.2 42.6L -1.1 65.5L 9.3 90.4L 23.7 119.2L 36.5 151.4L 44.4 185L 47.1 217.8L 44.6 249L 36.1 279.4L 20.9 311.5L 3.9 349.1"></path><path class="a__line js-line" d="M 30.7 -9.6L 30.7 -9.6L 16.1 18.2L 7.8 42.5L 8.7 65.8L 18.1 90.9L 31.2 119.5L 42.7 151.1L 49.6 183.9L 51.2 216.2L 47.2 247.5L 36.8 279L 21.4 313.6L 8.6 354.1"></path><path class="a__line js-line" d="M 40.6 -9.6L 40.6 -9.6L 26.3 18.3L 18 42.9L 18.3 66.5L 26.2 91.8L 37.6 120L 47.7 151L 53.4 183.1L 53.7 215.2L 48.1 247L 36.7 280.2L 23.2 317.5L 17.1 360.1"></path><path class="a__line js-line" d="M 51.3 -9.5L 51.3 -9.5L 37.2 18.7L 28.5 43.8L 27.6 67.8L 33.9 93.1L 43.3 121.1L 51.6 151.4L 55.9 183.1L 54.8 215.1L 48.1 248L 37.2 283.3L 28 323.1L 30.6 366.4"></path><path class="a__line js-line" d="M 62.9 -9.3L 62.9 -9.3L 48.7 19.4L 39.4 45.1L 37.1 69.6L 41.3 95L 48.5 122.7L 54.9 152.6L 57.5 184.1L 55.3 216.5L 48.4 250.8L 40 288.3L 37.4 329.7L 49.4 371.6"></path><path class="a__line js-line" d="M 75.3 -9.1L 75.3 -9.1L 61.1 20.3L 51 46.7L 47 71.8L 48.9 97.5L 53.8 125.2L 58.1 154.9L 59.2 186.4L 56.4 219.7L 50.8 255.4L 47 294.6L 52.6 336.1L 72.3 374.6"></path><path class="a__line js-line" d="M 88.5 -9L 88.5 -9L 74.3 21.2L 63.4 48.6L 57.8 74.5L 57.3 100.7L 59.8 128.5L 62.1 158.3L 62.2 190.2L 59.8 224.5L 57.1 261.6L 59.6 301.3L 73.2 340.9L 97 374.5"></path><path class="a__line js-line" d="M 102.4 -9.1L 102.4 -9.1L 88.5 22.1L 76.8 50.5L 69.7 77.4L 67.1 104.3L 67.4 132.5L 68.1 162.8L 67.8 195.4L 67 230.5L 68.8 268.3L 78.1 307.1L 97.4 343L 120.3 370.9"></path><path class="a__line js-line" d="M 116.9 -9.5L 116.9 -9.5L 103.5 22.8L 91.5 52.4L 83.1 80.4L 78.7 108.2L 77.3 137.2L 77.1 168.1L 77.3 201.3L 79.3 236.9L 86.2 274.2L 101.1 310.5L 122.3 341.5L 139 364.6"></path><path class="a__line js-line" d="M 131.7 -10.3L 131.7 -10.3L 119.2 23L 107.3 53.9L 98.1 83.1L 92.6 112.1L 90.1 141.9L 89.8 173.5L 91.5 207.2L 96.8 242.5L 108.3 278.1L 126.2 310.6L 144.4 336.8L 150.8 357"></path><path class="a__line js-line" d="M 146.6 -11.7L 146.6 -11.7L 135.4 22.7L 124.1 54.8L 114.9 85.4L 108.8 115.4L 106.1 146.2L 106.4 178.3L 110.1 211.9L 118.7 246.2L 132.9 278.9L 149.8 307.2L 160.5 329.7L 155.3 349.9"></path><path class="a__line js-line" d="M 161.3 -13.5L 161.3 -13.5L 151.8 21.7L 141.6 54.9L 132.9 86.7L 127.1 117.9L 124.9 149.4L 126.3 181.7L 132.1 214.6L 142.8 246.9L 156.8 276.3L 168.7 301L 169.4 322L 154 344.9"></path><path class="a__line js-line" d="M 175.4 -16L 175.4 -16L 167.9 19.9L 159.3 54.1L 151.8 86.9L 146.9 119L 145.6 150.9L 148.4 183L 155.5 214.7L 166.2 244.5L 176.9 270.8L 180.8 293.5L 171.4 315.6L 149.7 343"></path><path class="a__line js-line" d="M 188.6 -18.9L 188.6 -18.9L 183.3 17.4L 176.6 52.4L 170.6 85.9L 167 118.5L 166.9 150.5L 170.6 181.9L 177.8 211.9L 186.2 239.4L 191 263.7L 185.9 286.4L 168.8 311.8L 145.7 344.5"></path><path class="a__line js-line" d="M 200.5 -22.1L 200.5 -22.1L 197.5 14.3L 192.9 49.6L 188.7 83.6L 186.5 116.4L 187.3 148.1L 191.1 178.6L 196.8 207L 200.8 232.7L 198.3 256.6L 185.4 281.3L 164.4 311.2L 144.7 348.6"></path><path class="a__line js-line" d="M 211 -25.5L 211 -25.5L 210.1 10.8L 207.6 46.2L 205.1 80.3L 204.1 112.9L 205.3 144.1L 208.2 173.5L 210.8 200.7L 209.3 225.9L 199.8 250.9L 182 279.1L 161.5 313.6L 148.4 354.3"></path><path class="a__line js-line" d="M 220.1 -28.9L 220.1 -28.9L 221 7.1L 220.3 42.3L 219.3 76.2L 219.1 108.5L 220 139L 221 167.6L 219.5 194.2L 212.4 220.1L 197.7 247.6L 178.4 279.9L 162.2 318.3L 157.1 360.5"></path><path class="a__line js-line" d="M 227.8 -32L 227.8 -32L 230.2 3.4L 231 38.2L 231 71.7L 231 103.6L 230.9 133.6L 229.2 161.7L 223.5 188.7L 211.8 216.3L 194.7 247.1L 177.4 283.2L 167.6 324L 170.2 366"></path><path class="a__line js-line" d="M 234.4 -34.7L 234.4 -34.7L 237.8 0.1L 239.6 34.3L 240.2 67.4L 239.9 98.8L 238.2 128.4L 233.6 156.8L 224.3 184.9L 209.7 215L 192.9 249.2L 180.1 288.1L 177.5 329.7L 186.3 370.3"></path><path class="a__line js-line" d="M 240.2 -37L 240.2 -37L 244.2 -2.8L 246.5 30.9L 247.2 63.4L 246.1 94.5L 242.7 124.2L 235.4 153.3L 223.5 183.2L 208.2 215.9L 194.1 253L 187.1 293.5L 191 334.7L 203.9 373.1"></path><path class="a__line js-line" d="M 245.6 -38.7L 245.6 -38.7L 249.8 -5.2L 252.3 28L 252.6 60.2L 250.6 91.2L 245.3 121.2L 235.9 151.4L 222.8 183.3L 208.8 218.6L 198.8 257.6L 197.9 298.7L 206.8 338.5L 221.8 374.6"></path><path class="a__line js-line" d="M 250.9 -39.9L 250.9 -39.9L 255 -6.9L 257.3 25.9L 257.1 57.8L 253.9 88.8L 247.2 119.5L 236.6 151L 223.6 185L 212.1 222.3L 207 262.4L 211.3 303L 223.6 341L 239.1 375"></path><path class="a__line js-line" d="M 256.4 -40.6L 256.4 -40.6L 260.2 -8L 262.1 24.4L 261.2 56.2L 257 87.5L 249.2 119L 238.2 151.9L 226.5 187.6L 218.4 226.4L 217.9 266.8L 226.2 306.2L 240.3 342.4L 255.2 374.7"></path><path class="a__line js-line" d="M 262.4 -40.9L 262.4 -40.9L 265.7 -8.6L 267.1 23.5L 265.5 55.3L 260.5 87L 252.1 119.4L 241.4 153.6L 231.6 190.7L 227 230.2L 230.6 270.3L 241.7 308.4L 256.4 342.9L 270 374"></path><path class="a__line js-line" d="M 269 -41L 269 -41L 271.8 -8.9L 272.6 23.1L 270.4 55L 264.8 87.1L 256.1 120.4L 246.3 155.7L 238.7 193.8L 237.4 233.6L 244.1 273L 256.8 309.8L 271.3 343L 283.5 373.3"></path><path class="a__line js-line" d="M 276.3 -40.9L 276.3 -40.9L 278.6 -9L 278.9 23L 276.2 55.1L 270.1 87.6L 261.5 121.6L 252.7 157.9L 247.4 196.6L 248.8 236.4L 257.7 275L 271.2 310.5L 285 342.7L 295.7 372.6"></path><path class="a__line js-line" d="M 284.4 -40.8L 284.4 -40.8L 286.3 -8.9L 286.1 23.1L 282.9 55.3L 276.5 88.3L 268.1 122.9L 260.4 159.8L 257 198.8L 260.6 238.4L 270.9 276.2L 284.6 310.8L 297.5 342.4L 306.9 372.3"></path><path class="a__line js-line" d="M 293.4 -40.8L 293.4 -40.8L 294.8 -8.9L 294.2 23.2L 290.6 55.6L 284 88.9L 275.9 123.9L 269.1 161.3L 267.1 200.5L 272.2 239.8L 283.3 276.9L 296.8 311L 308.8 342.2L 317.2 372.2"></path><path class="a__line js-line" d="M 303.2 -40.7L 303.2 -40.7L 304.3 -8.8L 303.3 23.3L 299.3 55.8L 292.5 89.3L 284.6 124.7L 278.4 162.4L 277.4 201.6L 283.4 240.6L 294.7 277.3L 307.8 311L 318.9 342.2L 326.5 372.4"></path><path class="a__line js-line" d="M 313.9 -40.8L 313.9 -40.8L 314.6 -8.9L 313.3 23.3L 309 55.8L 302 89.4L 294 125L 288.1 162.8L 287.6 202.1L 293.9 240.9L 305.1 277.4L 317.6 311L 328.1 342.3L 334.9 372.9"></path><path class="a__line js-line" d="M 325.5 -40.9L 325.5 -40.9L 325.9 -8.9L 324.3 23.2L 319.7 55.7L 312.4 89.3L 304.2 124.9L 298.2 162.7L 297.6 202L 303.6 240.7L 314.4 277.2L 326.3 310.9L 336.1 342.6L 342.4 373.5"></path><path class="a__line js-line" d="M 338 -41L 338 -41L 338.2 -9L 336.3 23.1L 331.4 55.5L 323.7 88.9L 315.1 124.4L 308.5 162.1L 307.3 201.3L 312.7 240.1L 322.8 276.8L 334 310.8L 343.2 342.9L 349 374.2"></path><path class="a__line js-line" d="M 351.4 -41L 351.4 -41L 351.3 -9L 349.2 23L 344 55.2L 336 88.4L 326.8 123.5L 319.3 161L 317.1 200.1L 321.3 239L 330.3 276L 340.7 310.4L 349.3 343L 354.6 374.8"></path><path class="a__line js-line" d="M 365.5 -40.7L 365.5 -40.7L 365.3 -8.7L 363 23.1L 357.7 55L 349.4 87.7L 339.4 122.4L 330.8 159.4L 327 198.3L 329.6 237.3L 337.2 274.7L 346.6 309.8L 354.6 342.8L 359.3 375"></path><path class="a__line js-line" d="M 380.3 -40L 380.3 -40L 380 -8L 377.7 23.6L 372.3 55.1L 363.7 87.2L 353.1 121.1L 343.2 157.5L 337.5 196.1L 338.1 235.2L 344 273L 352.1 308.6L 359.2 342.2L 363.4 374.8"></path><path class="a__line js-line" d="M 395.4 -38.7L 395.4 -38.7L 395 -6.8L 392.8 24.6L 387.6 55.6L 379.1 87L 367.9 120L 356.7 155.5L 348.9 193.5L 347.1 232.5L 350.9 270.6L 357.4 306.8L 363.6 341L 367.2 373.9"></path><path class="a__line js-line" d="M 410.5 -36.9L 410.5 -36.9L 410.1 -5L 408.1 26.2L 403.4 56.7L 395.1 87.3L 383.8 119.2L 371.4 153.6L 361.6 190.7L 357.2 229.4L 358.4 267.7L 363.1 304.4L 368.1 339.1L 371.2 372.3"></path><path class="a__line js-line" d="M 425 -34.3L 425 -34.3L 424.7 -2.5L 423.1 28.5L 419 58.5L 411.6 88.3L 400.5 119L 387.4 152.1L 375.7 188.1L 368.6 226.1L 367.1 264.3L 369.7 301.4L 373.3 336.5L 375.7 369.9"></path><path class="a__line js-line" d="M 438.6 -31.1L 438.6 -31.1L 438.4 0.6L 437.2 31.4L 434.1 61.1L 427.8 90.1L 417.6 119.6L 404.3 151.2L 391.1 185.7L 381.6 222.8L 377.3 260.7L 377.5 297.9L 379.6 333.3L 381.2 366.9"></path><path class="a__line js-line" d="M 450.6 -27.5L 450.6 -27.5L 450.5 4.2L 450 34.9L 448 64.3L 443.3 92.6L 434.5 121L 421.8 151.1L 407.8 184L 396.2 219.8L 389.3 257.1L 387.1 294.1L 387.5 329.7L 388.3 363.4"></path><path class="a__line js-line" d="M 460.9 -23.7L 460.9 -23.7L 460.9 8.1L 461 38.7L 460.4 67.9L 457.6 95.8L 450.7 123.2L 439.3 151.9L 425.3 183.1L 412.2 217.4L 403 253.7L 398.4 290.3L 397.1 325.9L 397 359.7"></path><path class="a__line js-line" d="M 469.4 -19.9L 469.4 -19.9L 469.5 11.8L 470.2 42.5L 471 71.7L 470.2 99.4L 465.7 126.2L 456.2 153.5L 442.9 183.1L 429.1 215.8L 418.1 250.9L 411.5 286.8L 408.5 322.1L 407.6 355.8"></path><path class="a__line js-line" d="M 476.2 -16.6L 476.2 -16.6L 476.5 15.2L 477.8 46L 479.8 75.4L 481 103.1L 479 129.5L 472 155.9L 460.3 184L 446.6 215L 434.5 248.7L 426.2 283.8L 421.8 318.6L 420.1 352.1"></path><path class="a__line js-line" d="M 481.9 -13.8L 481.9 -13.8L 482.3 18L 484 49L 487.1 78.7L 490.1 106.7L 490.8 133L 486.5 158.8L 477 185.8L 464.2 215.2L 451.8 247.4L 442.3 281.3L 436.7 315.5L 434.3 348.8"></path><path class="a__line js-line" d="M 487 -11.8L 487 -11.8L 487.3 20.1L 489.3 51.4L 493.2 81.5L 497.9 109.9L 500.8 136.6L 499.5 162.1L 492.6 188.2L 481.5 216.3L 469.4 247L 459.5 279.7L 453 313.1L 450.2 346.1"></path><path class="a__line js-line" d="M 491.8 -10.3L 491.8 -10.3L 492.1 21.6L 494.2 53.1L 498.7 83.6L 504.5 112.7L 509.5 139.9L 510.9 165.5L 506.8 191.1L 498 218.1L 487.1 247.5L 477.3 279L 470.5 311.6L 467.4 344.1"></path><path class="a__line js-line" d="M 496.8 -9.5L 496.8 -9.5L 497 22.5L 499.2 54.2L 503.8 85.2L 510.4 114.9L 517 142.8L 520.8 168.9L 519.6 194.3L 513.4 220.6L 504.3 248.9L 495.3 279.3L 488.7 311L 485.5 343.1"></path><path class="a__line js-line" d="M 502.4 -9.1L 502.4 -9.1L 502.5 22.9L 504.5 54.8L 509.1 86.2L 516 116.6L 523.6 145.2L 529.4 172L 530.9 197.7L 527.4 223.7L 520.6 251.2L 512.9 280.6L 507 311.5L 504.1 343.2"></path><path class="a__line js-line" d="M 508.7 -9L 508.7 -9L 508.6 23L 510.3 55L 514.7 86.7L 521.6 117.7L 529.8 147.2L 537 174.8L 540.8 201L 540 227L 535.6 254.1L 529.7 282.8L 524.8 313.1L 522.4 344.5"></path><path class="a__line js-line" d="M 515.9 -9.1L 515.9 -9.1L 515.5 22.9L 516.9 55L 520.9 87L 527.5 118.4L 535.8 148.6L 543.8 177.1L 549.3 204.1L 551 230.5L 549.1 257.5L 545.2 285.8L 541.6 315.7L 539.8 346.9"></path><path class="a__line js-line" d="M 524 -9.2L 524 -9.2L 523.3 22.7L 524.3 54.8L 527.8 87L 533.8 118.8L 541.8 149.7L 550.1 179L 556.9 206.9L 560.5 233.9L 560.8 261.2L 559 289.5L 556.9 319.1L 555.7 350.2"></path><path class="a__line js-line" d="M 532.9 -9.3L 532.9 -9.3L 531.9 22.6L 532.5 54.7L 535.3 86.9L 540.7 119L 548.1 150.3L 556.2 180.5L 563.6 209.3L 568.6 237.1L 570.8 264.9L 570.8 293.4L 570.1 323.2L 569.6 354.3"></path><path class="a__line js-line" d="M 542.7 -9.3L 542.7 -9.3L 541.3 22.5L 541.4 54.5L 543.6 86.8L 548.1 119L 554.7 150.7L 562.3 181.5L 569.7 211.2L 575.5 240L 579 268.5L 580.6 297.5L 581 327.5L 581 358.7"></path><path class="a__line js-line" d="M 553.4 -9.2L 553.4 -9.2L 551.5 22.6L 551.1 54.5L 552.6 86.7L 556.2 118.9L 561.7 150.9L 568.5 182.3L 575.5 212.7L 581.4 242.4L 585.8 271.8L 588.3 301.4L 589.6 331.8L 589.9 363.2"></path><path class="a__line js-line" d="M 564.8 -9.1L 564.8 -9.1L 562.5 22.7L 561.5 54.6L 562.2 86.6L 564.9 118.9L 569.3 151L 575 182.7L 581.1 213.8L 586.7 244.3L 591.2 274.5L 594.3 304.8L 595.9 335.7L 596.3 367.4"></path><path class="a__line js-line" d="M 577.1 -9L 577.1 -9L 574.3 22.8L 572.6 54.7L 572.5 86.7L 574.1 118.8L 577.3 151L 581.7 182.9L 586.7 214.5L 591.6 245.7L 595.8 276.6L 598.7 307.6L 600.4 338.9L 600.6 370.8"></path><path class="a__line js-line" d="M 590.2 -9L 590.2 -9L 586.7 23L 584.3 54.8L 583.3 86.7L 583.9 118.8L 585.8 150.9L 588.8 183L 592.5 214.9L 596.3 246.6L 599.6 278.1L 602.1 309.6L 603.4 341.3L 603.4 373.3"></path><path class="a__line js-line" d="M 603.9 -9.2L 603.9 -9.2L 599.9 23L 596.7 55L 594.7 86.9L 594.1 118.8L 594.7 150.9L 596.3 182.9L 598.5 215L 601 247L 603.3 278.9L 604.9 310.7L 605.6 342.7L 605.2 374.7"></path><path class="a__line js-line" d="M 618.1 -9.7L 618.1 -9.7L 613.6 22.8L 609.6 55L 606.7 87L 604.8 118.9L 604.1 150.8L 604.2 182.8L 604.9 214.9L 605.9 246.9L 606.9 279L 607.6 311L 607.6 343L 606.8 375"></path><path class="a__line js-line" d="M 632.7 -10.6L 632.7 -10.6L 627.8 22.4L 623.1 54.9L 619.1 87L 616 118.9L 613.8 150.8L 612.3 182.6L 611.5 214.6L 611.1 246.5L 610.8 278.5L 610.5 310.4L 609.9 342.3L 608.7 374.2"></path><path class="a__line js-line" d="M 647.4 -11.9L 647.4 -11.9L 642.2 21.6L 636.9 54.5L 631.9 86.9L 627.5 119L 623.8 150.8L 620.8 182.5L 618.5 214.1L 616.7 245.8L 615.3 277.5L 614.1 309.2L 612.9 340.8L 611.6 372.4"></path><path class="a__line js-line" d="M 661.9 -13.8L 661.9 -13.8L 656.7 20.3L 651 53.8L 645 86.7L 639.3 119L 634.1 150.8L 629.6 182.3L 625.8 213.6L 622.8 244.8L 620.4 276L 618.5 307.3L 617 338.6L 615.7 370"></path><path class="a__line js-line" d="M 675.7 -16.2L 675.7 -16.2L 671 18.6L 665.1 52.8L 658.4 86.3L 651.4 119L 644.7 150.9L 638.6 182.1L 633.5 213L 629.4 243.6L 626.3 274.3L 624 305L 622.4 335.9L 621.3 367"></path><path class="a__line js-line" d="M 688.7 -19L 688.7 -19L 684.7 16.5L 679 51.5L 671.8 85.7L 663.7 118.8L 655.4 150.9L 647.8 182L 641.3 212.3L 636.4 242.3L 632.9 272.3L 630.6 302.4L 629.3 332.9L 628.5 363.8"></path><path class="a__line js-line" d="M 700.5 -22.2L 700.5 -22.2L 697.7 14L 692.6 49.8L 685.1 84.8L 676 118.6L 666.3 150.9L 657.1 181.9L 649.5 211.7L 643.9 240.9L 640.4 270.1L 638.4 299.7L 637.5 329.8L 637.2 360.6"></path><path class="a__line js-line" d="M 711 -25.5L 711 -25.5L 709.8 11.3L 705.6 47.9L 698.3 83.8L 688.4 118.3L 677.3 151L 666.6 181.7L 657.8 211L 651.9 239.5L 648.6 267.9L 647.2 296.9L 647 326.7L 647.2 357.4"></path><path class="a__line js-line" d="M 720.1 -28.7L 720.1 -28.7L 720.9 8.4L 718.1 45.7L 711.2 82.5L 700.8 117.9L 688.3 151L 676.1 181.6L 666.4 210.3L 660.2 238L 657.5 265.8L 657 294.3L 657.6 323.9L 658.3 354.5"></path><path class="a__line js-line" d="M 728 -31.8L 728 -31.8L 730.9 5.6L 729.7 43.4L 723.8 81.1L 713 117.4L 699.3 151L 685.7 181.5L 675 209.6L 669 236.5L 667 263.6L 667.7 291.7L 669.2 321.2L 670.3 352"></path><path class="a__line js-line" d="M 734.8 -34.4L 734.8 -34.4L 739.9 2.8L 740.7 41.2L 735.9 79.7L 725.1 116.8L 710.3 151L 695.2 181.4L 683.8 208.9L 678 235L 677.1 261.5L 679 289.4L 681.4 318.9L 682.9 349.7"></path><path class="a__line js-line" d="M 740.9 -36.7L 740.9 -36.7L 748.1 0.4L 751 39L 747.6 78.2L 736.9 116.3L 721.1 151L 704.7 181.3L 692.7 208.1L 687.4 233.5L 687.7 259.6L 690.9 287.3L 694.2 316.9L 695.9 347.9"></path><path class="a__line js-line" d="M 746.5 -38.4L 746.5 -38.4L 755.8 -1.8L 760.7 36.9L 758.8 76.8L 748.5 115.7L 731.7 150.9L 714.2 181.2L 701.7 207.4L 697.1 232.1L 698.8 257.8L 703.2 285.5L 707.3 315.2L 709.2 346.4"></path><path class="a__line js-line" d="M 752.1 -39.6L 752.1 -39.6L 763.1 -3.5L 770.1 35.1L 769.7 75.5L 759.7 115.2L 742.2 150.9L 723.5 181L 710.8 206.5L 707 230.6L 710.1 256.1L 715.9 283.9L 720.6 313.9L 722.6 345.3"></path><path class="a__line js-line" d="M 757.8 -40.4L 757.8 -40.4L 770.3 -4.9L 779.1 33.6L 780.3 74.4L 770.7 114.8L 752.3 150.9L 732.6 180.8L 719.9 205.6L 717.2 229.2L 721.8 254.5L 728.8 282.6L 734 312.9L 735.9 344.4"></path><path class="a__line js-line" d="M 764 -40.8L 764 -40.8L 777.7 -6L 788.1 32.4L 790.6 73.5L 781.3 114.5L 762.2 150.9L 741.6 180.4L 729.1 204.7L 727.7 227.8L 733.8 253.1L 741.8 281.5L 747.4 312.1L 749.1 343.8"></path><path class="a__line js-line" d="M 770.7 -41L 770.7 -41L 785.5 -6.7L 797.2 31.5L 800.8 72.9L 791.6 114.3L 771.6 150.9L 750.5 180L 738.3 203.6L 738.4 226.4L 746.1 251.8L 754.9 280.6L 760.8 311.5L 762.2 343.4"></path><path class="a__line js-line" d="M 778.2 -41L 778.2 -41L 793.7 -7.2L 806.6 30.9L 810.8 72.6L 801.5 114.4L 780.7 151L 759.1 179.5L 747.7 202.4L 749.4 224.9L 758.5 250.7L 768.1 279.9L 774.1 311.2L 775.1 343.1"></path><path class="a__line js-line" d="M 786.5 -41L 786.5 -41L 802.6 -7.5L 816.2 30.6L 820.9 72.5L 811.1 114.6L 789.4 151L 767.5 178.9L 757.3 201.1L 760.7 223.5L 771.2 249.7L 781.4 279.5L 787.2 311L 787.8 343"></path><path class="a__line js-line" d="M 795.6 -40.9L 795.6 -40.9L 812.2 -7.6L 826.2 30.6L 830.8 72.8L 820.2 115L 797.6 151L 775.8 178L 767 199.7L 772.3 222.2L 784.1 248.8L 794.6 279.1L 800.3 311L 800.3 343"></path><path class="a__line js-line" d="M 805.5 -40.9L 805.5 -40.9L 822.5 -7.5L 836.6 30.9L 840.6 73.4L 828.9 115.5L 805.3 150.9L 784 177L 777 198.1L 784.3 220.8L 797.2 248.1L 807.9 279L 813.2 311.1L 812.7 343.1"></path><path class="a__line js-line" d="M 816.3 -40.9L 816.3 -40.9L 833.5 -7.3L 847.3 31.6L 850.3 74.4L 836.9 116.2L 812.5 150.7L 792.2 175.7L 787.4 196.4L 796.7 219.5L 810.5 247.6L 821.2 279L 826 311.4L 825 343.3"></path><path class="a__line js-line" d="M 828 -41L 828 -41L 845.2 -6.8L 858.3 32.6L 859.6 75.7L 844.4 117L 819.4 150.3L 800.4 174.2L 798.2 194.6L 809.5 218.3L 824 247.2L 834.4 279.2L 838.8 311.8L 837.2 343.5"></path><path class="a__line js-line" d="M 840.6 -41L 840.6 -41L 857.6 -6L 869.4 34L 868.5 77.2L 851.1 117.8L 825.8 149.6L 808.9 172.4L 809.5 192.8L 822.8 217.2L 837.7 247L 847.7 279.6L 851.4 312.3L 849.3 343.9"></path><path class="a__line js-line" d="M 854 -40.8L 854 -40.8L 870.4 -4.9L 880.4 35.9L 876.7 79L 857.2 118.5L 832 148.6L 817.7 170.3L 821.5 190.8L 836.4 216.3L 851.5 247L 860.9 280.1L 864 312.9L 861.4 344.2"></path><path class="a__line js-line" d="M 868.1 -40.3L 868.1 -40.3L 883.5 -3.3L 891 38.2L 884 81L 862.5 118.9L 838.1 147.1L 827.1 167.9L 834.1 189L 850.5 215.6L 865.4 247.3L 874 280.9L 876.5 313.6L 873.4 344.7"></path><path class="a__line js-line" d="M 882.8 -39.4L 882.8 -39.4L 896.5 -1.2L 900.8 40.8L 890.4 82.9L 867.1 119L 844.4 145.3L 837.2 165.4L 847.4 187.2L 864.9 215.1L 879.3 247.9L 887 281.8L 888.8 314.4L 885.4 345.3"></path><path class="a__line js-line" d="M 897.7 -38L 897.7 -38L 909 1.4L 909.6 43.7L 895.7 84.7L 871.4 118.5L 851.2 142.9L 848.2 162.7L 861.5 185.6L 879.6 215L 893.2 248.7L 899.9 282.9L 901.1 315.4L 897.4 345.9"></path><path class="a__line js-line" d="M 912.4 -35.9L 912.4 -35.9L 920.6 4.5L 917.1 46.6L 899.9 86L 875.5 117.5L 858.7 140.1L 860.2 160L 876.2 184.3L 894.5 215.2L 907 249.8L 912.6 284.2L 913.2 316.6L 909.3 346.6"></path><path class="a__line js-line" d="M 926.6 -33.1L 926.6 -33.1L 930.9 7.9L 923.2 49.4L 903.3 86.9L 879.8 115.8L 867.3 137L 873.4 157.4L 891.6 183.4L 909.5 215.9L 920.5 251.2L 925 285.7L 925.2 317.8L 921.2 347.5"></path><path class="a__line js-line" d="M 939.5 -29.8L 939.5 -29.8L 939.7 11.5L 927.9 51.9L 906.2 86.9L 884.7 113.4L 877.1 133.7L 887.6 155.1L 907.3 183L 924.2 216.9L 933.7 252.8L 937.1 287.4L 936.9 319.2L 932.9 348.4"></path><path class="a__line js-line" d="M 950.9 -26.1L 950.9 -26.1L 946.7 15L 931.4 53.7L 909 86.2L 890.7 110.5L 888.4 130.4L 902.8 153.2L 923.2 183.1L 938.7 218.4L 946.4 254.7L 948.8 289.2L 948.4 320.7L 944.6 349.4"></path><path class="a__line js-line" d="M 960.5 -22.3L 960.5 -22.3L 952.1 18.1L 934.1 54.8L 912.2 84.6L 898.1 107.1L 901.1 127.2L 918.7 151.9L 939 183.8L 952.6 220.2L 958.5 256.8L 960 291.1L 959.5 322.2L 956.1 350.5"></path><path class="a__line js-line" d="M 968.3 -18.6L 968.3 -18.6L 956.2 20.5L 936.5 54.9L 916.3 82.3L 907 103.5L 915.1 124.4L 935 151.1L 954.4 185L 965.9 222.3L 970 259L 970.8 293.1L 970.4 323.8L 967.4 351.6"></path><path class="a__line js-line" d="M 974.3 -15.2L 974.3 -15.2L 959 22.2L 938.8 54.2L 921.6 79.3L 917.6 99.8L 930.3 122L 951.5 151.1L 969.4 186.7L 978.4 224.7L 980.8 261.3L 981 295L 980.8 325.4L 978.5 352.8"></path><path class="a__line js-line" d="M 978.4 -12.3L 978.4 -12.3L 961 23L 941.5 52.4L 928.4 75.7L 930 96.2L 946.5 120.3L 968 151.7L 983.6 188.8L 990.1 227.2L 991 263.6L 990.8 296.9L 991 327L 989.3 353.9"></path><path class="a__line js-line" d="M 981 -10.2L 981 -10.2L 962.5 22.7L 945.2 49.7L 937 71.7L 944.1 92.9L 963.5 119.2L 984.2 152.9L 997 191.3L 1000.9 229.8L 1000.5 265.8L 1000.1 298.7L 1000.9 328.4L 999.9 354.9"></path><path class="a__line js-line" d="M 982.3 -9.1L 982.3 -9.1L 964.2 21.2L 950.3 46.1L 947.8 67.5L 959.9 90.2L 980.9 119L 999.8 154.8L 1009.4 194L 1010.9 232.4L 1009.5 267.9L 1009.2 300.3L 1010.6 329.7L 1010.3 355.8"></path><path class="a__line js-line" d="M 983 -9.3L 983 -9.3L 966.8 18.5L 957.5 41.9L 960.8 63.5L 976.9 88.2L 998.3 119.7L 1014.4 157.3L 1020.7 196.9L 1020.1 234.8L 1018 269.8L 1018 301.8L 1020.1 330.7L 1020.6 356.5"></path><path class="a__line js-line" d="M 983.9 -10.9L 983.9 -10.9L 971 14.8L 967.2 37.3L 975.9 59.9L 994.9 87.1L 1015.2 121.2L 1027.9 160.1L 1031 199.7L 1028.6 237L 1026.3 271.4L 1026.8 303L 1029.7 331.6L 1030.7 356.9"></path><path class="a__line js-line" d="M 986 -13.9L 986 -13.9L 977.7 10.2L 979.6 32.7L 992.9 57.2L 1013.1 87.1L 1031.1 123.4L 1040.1 163.2L 1040.3 202.4L 1036.7 239L 1034.4 272.7L 1035.6 303.9L 1039.3 332.2L 1040.8 357.2"></path><path class="a__line js-line" d="M 990.2 -18L 990.2 -18L 987.2 5.3L 994.5 28.7L 1011.2 55.5L 1031.1 88.2L 1045.8 126.2L 1051 166.3L 1048.8 204.9L 1044.5 240.6L 1042.6 273.8L 1044.6 304.6L 1049 332.5L 1050.8 357.1"></path><path class="a__line js-line" d="M 997.3 -22.9L 997.3 -22.9L 999.8 0.5L 1011.6 25.6L 1030 55L 1048.1 90.2L 1059 129.4L 1060.7 169.3L 1056.7 207L 1052.3 242L 1051 274.6L 1053.9 305L 1059 332.6L 1060.7 356.8"></path><path class="a__line js-line" d="M 1007.6 -28L 1007.6 -28L 1015.1 -3.7L 1030.2 23.6L 1048.7 55.7L 1063.8 92.9L 1070.7 132.7L 1069.4 172L 1064.3 208.8L 1060.1 243L 1059.8 275.2L 1063.6 305.2L 1069.2 332.3L 1070.5 356.1"></path><path class="a__line js-line" d="M 1021.1 -32.8L 1021.1 -32.8L 1032.7 -6.8L 1049.4 23L 1066.3 57.6L 1077.8 96.1L 1080.9 135.9L 1077.4 174.3L 1071.9 210.2L 1068.3 243.7L 1069 275.5L 1073.8 305.1L 1079.6 331.7L 1080 355.1"></path><path class="a__line js-line" d="M 1037.2 -36.7L 1037.2 -36.7L 1051.5 -8.6L 1068.2 23.7L 1082.6 60.2L 1090.2 99.4L 1090.1 138.8L 1085.1 176.3L 1079.6 211.2L 1077 244.2L 1078.8 275.6L 1084.4 304.8L 1090.1 330.8L 1089.2 353.7"></path><path class="a__line js-line" d="M 1055.2 -39.4L 1055.2 -39.4L 1070.6 -9L 1086 25.4L 1097 63.3L 1101 102.7L 1098.4 141.3L 1092.6 177.8L 1087.7 212L 1086.3 244.5L 1089.2 275.4L 1095.4 304L 1100.6 329.4L 1097.9 352.1"></path><path class="a__line js-line" d="M 1074 -40.8L 1074 -40.8L 1089.1 -8.2L 1102.2 28L 1109.7 66.6L 1110.5 105.7L 1106.3 143.4L 1100.4 178.9L 1096.4 212.4L 1096.2 244.5L 1100.3 275L 1106.8 302.9L 1111 327.6L 1105.8 350.2"></path><path class="a__line js-line" d="M 1092.6 -40.9L 1092.6 -40.9L 1106.4 -6.4L 1116.6 30.9L 1120.9 69.7L 1119.3 108.2L 1114 145L 1108.6 179.6L 1105.8 212.6L 1106.9 244.3L 1111.9 274.2L 1118.3 301.4L 1120.8 325.4L 1112.7 348.3"></path><path class="a__line js-line" d="M 1110.4 -40L 1110.4 -40L 1122.1 -4.1L 1129.4 33.9L 1130.8 72.6L 1127.5 110.3L 1122.1 146.1L 1117.5 180.1L 1115.9 212.6L 1118.3 243.8L 1123.9 273L 1129.8 299.4L 1129.9 322.9L 1118.5 346.3"></path><path class="a__line js-line" d="M 1126.9 -38.3L 1126.9 -38.3L 1136.2 -1.4L 1140.7 36.8L 1140 74.9L 1135.7 111.8L 1130.6 146.8L 1127.1 180.2L 1126.9 212.3L 1130.3 242.9L 1136.3 271.4L 1140.8 296.9L 1137.8 320.1L 1122.9 344.6"></path><path class="a__line js-line" d="M 1141.8 -36.2L 1141.8 -36.2L 1148.8 1.2L 1151 39.3L 1148.8 76.8L 1144.2 112.8L 1139.7 147.2L 1137.5 180.1L 1138.5 211.7L 1142.9 241.6L 1148.5 269.2L 1150.9 293.9L 1144.3 317.2L 1126.1 343.4"></path><path class="a__line js-line" d="M 1155.3 -34.1L 1155.3 -34.1L 1160.2 3.5L 1160.7 41.2L 1157.7 78.1L 1153.2 113.5L 1149.6 147.2L 1148.6 179.6L 1150.9 210.7L 1155.7 239.8L 1160.3 266.4L 1159.7 290.5L 1149 314.5L 1128.3 343"></path><path class="a__line js-line" d="M 1167.6 -32.2L 1167.6 -32.2L 1170.8 5.3L 1170.1 42.7L 1166.8 78.9L 1162.9 113.7L 1160.4 146.9L 1160.6 178.8L 1163.7 209.2L 1168.3 237.4L 1171 263.1L 1166.7 287L 1151.8 312.3L 1129.9 343.7"></path><path class="a__line js-line" d="M 1179 -30.6L 1179 -30.6L 1181 6.7L 1179.7 43.6L 1176.4 79.3L 1173.2 113.5L 1171.8 146.2L 1173.1 177.6L 1176.6 207.1L 1180.3 234.4L 1180.1 259.3L 1171.5 283.6L 1153.2 311.1L 1131.6 345.6"></path><path class="a__line js-line" d="M 1189.7 -29.5L 1189.7 -29.5L 1191 7.5L 1189.4 43.9L 1186.6 79.1L 1184.3 112.8L 1183.9 145.1L 1185.8 175.7L 1189.1 204.4L 1191 230.7L 1187.1 255.3L 1174 280.9L 1153.5 311.2L 1134.3 348.8"></path><path class="a__line js-line" d="M 1200 -28.9L 1200 -28.9L 1201 7.8L 1199.6 43.7L 1197.3 78.5L 1195.9 111.7L 1196.3 143.4L 1198.5 173.2L 1200.8 201L 1199.8 226.6L 1191.5 251.6L 1174.6 279.3L 1153.8 312.9L 1139.2 353.2"></path><path class="a__line js-line" d="M 1210.1 -28.9L 1210.1 -28.9L 1211 7.4L 1210 43L 1208.4 77.3L 1207.8 110L 1208.6 141L 1210.4 170.1L 1210.8 197L 1206.1 222.5L 1193.4 248.7L 1174.1 279.2L 1155.3 316.2L 1147 358.4"></path><path class="a__line js-line" d="M 1219.8 -29.4L 1219.8 -29.4L 1221 6.5L 1220.5 41.7L 1219.7 75.5L 1219.6 107.7L 1220.4 138L 1221 166.3L 1218.5 192.8L 1209.6 218.8L 1193.3 247.1L 1173.5 281L 1159.3 321L 1158.5 363.9"></path><path class="a__line js-line" d="M 1229.1 -30.4L 1229.1 -30.4L 1230.8 5.1L 1231 39.8L 1230.7 73.2L 1230.8 104.8L 1231 134.5L 1229.5 162.2L 1223.5 188.7L 1210.5 216.1L 1192 247.4L 1174.6 284.7L 1167 326.7L 1173.8 368.9"></path><path class="a__line js-line" d="M 1237.8 -32L 1237.8 -32L 1240 3.1L 1240.8 37.4L 1241 70.3L 1240.8 101.3L 1239.7 130.4L 1235.4 158L 1225.6 185.3L 1209.6 215L 1191.2 249.7L 1178.7 289.9L 1179 332.6L 1192.5 372.7"></path><path class="a__line js-line" d="M 1245.6 -33.9L 1245.6 -33.9L 1248.4 0.7L 1249.7 34.5L 1249.9 66.9L 1249 97.5L 1245.9 126.4L 1238.6 154.3L 1225.4 183.3L 1208 216L 1192.5 254L 1187.1 296.1L 1195.4 337.8L 1213.4 374.8"></path><path class="a__line js-line" d="M 1252.3 -36L 1252.3 -36L 1255.5 -2L 1257.1 31.3L 1257.1 63.3L 1255 93.7L 1249.5 122.7L 1239.1 151.8L 1223.9 183.2L 1207.6 219.1L 1197.7 259.8L 1200.4 302.1L 1215.3 341.5L 1235.1 374.7"></path><path class="a__line js-line" d="M 1257.5 -38.1L 1257.5 -38.1L 1261 -4.6L 1262.6 28.2L 1262 59.9L 1258.4 90.3L 1250.6 120.1L 1238 151L 1222.6 185.3L 1210.1 224.1L 1207.6 266.2L 1218.2 307.2L 1237.3 343L 1256 372.6"></path><path class="a__line js-line" d="M 1261.2 -39.8L 1261.2 -39.8L 1264.7 -7L 1266.1 25.5L 1264.7 57L 1259.5 87.9L 1249.8 119L 1236.5 152.3L 1223.4 189.4L 1217 230.3L 1222.7 272.1L 1239.4 310.3L 1259.6 342.1L 1274.6 368.6"></path><path class="a__line js-line" d="M 1263.5 -40.8L 1263.5 -40.8L 1266.7 -8.5L 1267.7 23.6L 1265.4 55.3L 1258.9 87L 1248.6 119.9L 1236.5 155.6L 1228 195.1L 1229.1 236.7L 1242.1 276.6L 1262.1 310.9L 1280.1 339L 1289.7 363.5"></path><path class="a__line js-line" d="M 1264.9 -40.9L 1264.9 -40.9L 1267.6 -9L 1268 23L 1264.9 55.1L 1258 88L 1248.3 122.8L 1239.6 160.7L 1237.4 201.5L 1246.1 242.2L 1264.3 278.8L 1284.2 309.1L 1297.5 334.3L 1300.9 358"></path><path class="a__line js-line" d="M 1266 -39.6L 1266 -39.6L 1268.1 -8L 1268 24L 1264.6 56.7L 1258.1 90.9L 1250.8 127.5L 1247 166.8L 1251.8 207.4L 1266.8 245.8L 1287 278.5L 1303.7 305.2L 1310.8 328.7L 1308.4 352.7"></path><path class="a__line js-line" d="M 1267.9 -37L 1267.9 -37L 1269.2 -5.6L 1268.7 26.6L 1265.7 60.1L 1260.8 95.4L 1257.2 133.2L 1259.3 173L 1270.5 212.1L 1289.3 247L 1308.2 275.8L 1319.5 299.9L 1320 323L 1312.9 348.4"></path><path class="a__line js-line" d="M 1271.5 -33.2L 1271.5 -33.2L 1272.2 -1.7L 1271.6 30.7L 1269.5 64.8L 1267.2 101L 1268.2 139.3L 1276.1 178.2L 1292 214.6L 1311.4 245.7L 1326.4 271.3L 1331 294.1L 1325.6 318.1L 1315.3 345.2"></path><path class="a__line js-line" d="M 1277.7 -28.4L 1277.7 -28.4L 1278 3.1L 1277.6 35.8L 1277 70.3L 1277.9 106.9L 1283.5 144.7L 1296.1 181.6L 1314.2 214.8L 1331.4 242.4L 1340.5 265.8L 1338.4 288.7L 1328.5 314.3L 1316.6 343.5"></path><path class="a__line js-line" d="M 1287.2 -23.3L 1287.2 -23.3L 1287.1 8.4L 1287.3 41.3L 1288.6 76L 1292.7 112.1L 1302.3 148.6L 1317.7 183L 1335.3 212.8L 1348.1 237.7L 1350.5 260.3L 1342.8 284.3L 1330 312L 1318 343"></path><path class="a__line js-line" d="M 1299.9 -18.3L 1299.9 -18.3L 1299.8 13.6L 1300.7 46.5L 1303.8 80.9L 1310.8 116.2L 1323 150.7L 1339 182.2L 1353.9 209.1L 1360.9 232.3L 1357 255.3L 1345.2 281.2L 1331.2 311L 1320 343.6"></path><path class="a__line js-line" d="M 1315.7 -14.1L 1315.7 -14.1L 1315.6 17.9L 1317.3 50.7L 1321.9 84.5L 1330.8 118.5L 1344 150.8L 1358.7 179.6L 1369.1 204.4L 1370.1 227.2L 1361 251.4L 1346.7 279.5L 1332.9 311.3L 1323.2 345.1"></path><path class="a__line js-line" d="M 1333.6 -11L 1333.6 -11L 1333.7 21.1L 1336 53.6L 1341.7 86.6L 1351.4 119L 1364.1 149.1L 1375.8 175.7L 1381 199.4L 1376.3 222.7L 1363.5 248.7L 1348.3 279L 1335.7 312.5L 1327.8 347"></path></svg> </a-waves><!-- .a-waves --> <div class="s__content js-content astro-7dteeuzc" style="clip-path: polygon(0px 0px, 100% 0px, 100% 100%, 0px 100%);"> <a-separator class="style--primary s__separator js-separator astro-7dteeuzc astro-rdpcjfuz is-in-view" data-intersect="" style="translate: none; rotate: none; scale: none; transform: translate(0px, 0%);"> <span class="a__triangle astro-rdpcjfuz"></span> <span class="a__binaries astro-rdpcjfuz"> <span class="a__code js-code astro-rdpcjfuz"> <span class="a__char js-char astro-rdpcjfuz a__char--0"> 1 </span><span class="a__char js-char astro-rdpcjfuz a__char--1"> 0 </span><span class="a__char js-char astro-rdpcjfuz a__char--1"> 0 </span><span class="a__char js-char astro-rdpcjfuz a__char--1"> 0 </span><span class="a__char js-char astro-rdpcjfuz a__char--1"> 0 </span><span class="a__char js-char astro-rdpcjfuz a__char--0"> 0 </span><span class="a__char js-char astro-rdpcjfuz a__char--1"> 1 </span><span class="a__char a__char--blank js-char astro-rdpcjfuz"> </span><span class="a__char js-char astro-rdpcjfuz a__char--1"> 1 </span><span class="a__char js-char astro-rdpcjfuz a__char--1"> 1 </span><span class="a__char js-char astro-rdpcjfuz a__char--0"> 0 </span><span class="a__char js-char astro-rdpcjfuz a__char--1"> 1 </span><span class="a__char js-char astro-rdpcjfuz a__char--1"> 1 </span><span class="a__char js-char astro-rdpcjfuz a__char--0"> 1 </span><span class="a__char js-char astro-rdpcjfuz a__char--0"> 0 </span><span class="a__char a__char--blank js-char astro-rdpcjfuz"> </span><span class="a__char js-char astro-rdpcjfuz a__char--1"> 1 </span><span class="a__char js-char astro-rdpcjfuz a__char--0"> 1 </span><span class="a__char js-char astro-rdpcjfuz a__char--1"> 1 </span><span class="a__char js-char astro-rdpcjfuz a__char--1"> 0 </span><span class="a__char js-char astro-rdpcjfuz a__char--1"> 1 </span><span class="a__char js-char astro-rdpcjfuz a__char--0"> 0 </span><span class="a__char js-char astro-rdpcjfuz a__char--0"> 0 </span> </span> <span class="a__stripes astro-rdpcjfuz"></span> <span class="a__code js-code astro-rdpcjfuz"> <span class="a__char js-char astro-rdpcjfuz a__char--0"> 1 </span><span class="a__char js-char astro-rdpcjfuz a__char--1"> 1 </span><span class="a__char js-char astro-rdpcjfuz a__char--0"> 0 </span><span class="a__char js-char astro-rdpcjfuz a__char--1"> 1 </span><span class="a__char js-char astro-rdpcjfuz a__char--0"> 1 </span><span class="a__char js-char astro-rdpcjfuz a__char--0"> 1 </span><span class="a__char js-char astro-rdpcjfuz a__char--1"> 1 </span><span class="a__char a__char--blank js-char astro-rdpcjfuz"> </span><span class="a__char js-char astro-rdpcjfuz a__char--1"> 1 </span><span class="a__char js-char astro-rdpcjfuz a__char--0"> 1 </span><span class="a__char js-char astro-rdpcjfuz a__char--0"> 0 </span><span class="a__char js-char astro-rdpcjfuz a__char--1"> 1 </span><span class="a__char js-char astro-rdpcjfuz a__char--1"> 0 </span><span class="a__char js-char astro-rdpcjfuz a__char--1"> 0 </span><span class="a__char js-char astro-rdpcjfuz a__char--0"> 1 </span> </span> <span class="a__stripes astro-rdpcjfuz"></span> <span class="a__code js-code astro-rdpcjfuz"> <span class="a__char js-char astro-rdpcjfuz a__char--0"> 1 </span><span class="a__char js-char astro-rdpcjfuz a__char--0"> 1 </span><span class="a__char js-char astro-rdpcjfuz a__char--0"> 0 </span><span class="a__char js-char astro-rdpcjfuz a__char--1"> 1 </span><span class="a__char js-char astro-rdpcjfuz a__char--1"> 1 </span><span class="a__char js-char astro-rdpcjfuz a__char--0"> 1 </span><span class="a__char js-char astro-rdpcjfuz a__char--1"> 0 </span><span class="a__char a__char--blank js-char astro-rdpcjfuz"> </span><span class="a__char js-char astro-rdpcjfuz a__char--0"> 1 </span><span class="a__char js-char astro-rdpcjfuz a__char--1"> 1 </span><span class="a__char js-char astro-rdpcjfuz a__char--1"> 0 </span><span class="a__char js-char astro-rdpcjfuz a__char--0"> 0 </span><span class="a__char js-char astro-rdpcjfuz a__char--0"> 1 </span><span class="a__char js-char astro-rdpcjfuz a__char--0"> 0 </span><span class="a__char js-char astro-rdpcjfuz a__char--0"> 1 </span> </span> <span class="a__stripes astro-rdpcjfuz"></span> <span class="a__code js-code astro-rdpcjfuz"> <span class="a__char js-char astro-rdpcjfuz a__char--1"> 1 </span><span class="a__char js-char astro-rdpcjfuz a__char--0"> 0 </span><span class="a__char js-char astro-rdpcjfuz a__char--1"> 1 </span><span class="a__char js-char astro-rdpcjfuz a__char--0"> 0 </span><span class="a__char js-char astro-rdpcjfuz a__char--1"> 1 </span><span class="a__char js-char astro-rdpcjfuz a__char--1"> 1 </span><span class="a__char js-char astro-rdpcjfuz a__char--0"> 1 </span><span class="a__char a__char--blank js-char astro-rdpcjfuz"> </span><span class="a__char js-char astro-rdpcjfuz a__char--0"> 1 </span><span class="a__char js-char astro-rdpcjfuz a__char--0"> 1 </span><span class="a__char js-char astro-rdpcjfuz a__char--1"> 0 </span><span class="a__char js-char astro-rdpcjfuz a__char--1"> 1 </span><span class="a__char js-char astro-rdpcjfuz a__char--0"> 1 </span><span class="a__char js-char astro-rdpcjfuz a__char--1"> 1 </span><span class="a__char js-char astro-rdpcjfuz a__char--0"> 1 </span> </span> <span class="a__stripes astro-rdpcjfuz"></span> <span class="a__code js-code astro-rdpcjfuz"> <span class="a__char js-char astro-rdpcjfuz a__char--0"> 1 </span><span class="a__char js-char astro-rdpcjfuz a__char--1"> 1 </span><span class="a__char js-char astro-rdpcjfuz a__char--1"> 0 </span><span class="a__char js-char astro-rdpcjfuz a__char--0"> 0 </span><span class="a__char js-char astro-rdpcjfuz a__char--1"> 1 </span><span class="a__char js-char astro-rdpcjfuz a__char--1"> 0 </span><span class="a__char js-char astro-rdpcjfuz a__char--0"> 0 </span><span class="a__char a__char--blank js-char astro-rdpcjfuz"> </span><span class="a__char js-char astro-rdpcjfuz a__char--1"> 1 </span><span class="a__char js-char astro-rdpcjfuz a__char--1"> 1 </span><span class="a__char js-char astro-rdpcjfuz a__char--1"> 0 </span><span class="a__char js-char astro-rdpcjfuz a__char--1"> 1 </span><span class="a__char js-char astro-rdpcjfuz a__char--0"> 1 </span><span class="a__char js-char astro-rdpcjfuz a__char--0"> 1 </span><span class="a__char js-char astro-rdpcjfuz a__char--1"> 0 </span> </span> <span class="a__stripes astro-rdpcjfuz"></span> <span class="a__code js-code astro-rdpcjfuz"> <span class="a__char js-char astro-rdpcjfuz a__char--1"> 1 </span><span class="a__char js-char astro-rdpcjfuz a__char--0"> 1 </span><span class="a__char js-char astro-rdpcjfuz a__char--1"> 0 </span><span class="a__char js-char astro-rdpcjfuz a__char--0"> 1 </span><span class="a__char js-char astro-rdpcjfuz a__char--0"> 0 </span><span class="a__char js-char astro-rdpcjfuz a__char--0"> 0 </span><span class="a__char js-char astro-rdpcjfuz a__char--1"> 1 </span><span class="a__char a__char--blank js-char astro-rdpcjfuz"> </span><span class="a__char js-char astro-rdpcjfuz a__char--0"> 1 </span><span class="a__char js-char astro-rdpcjfuz a__char--0"> 1 </span><span class="a__char js-char astro-rdpcjfuz a__char--0"> 0 </span><span class="a__char js-char astro-rdpcjfuz a__char--0"> 0 </span><span class="a__char js-char astro-rdpcjfuz a__char--0"> 0 </span><span class="a__char js-char astro-rdpcjfuz a__char--1"> 0 </span><span class="a__char js-char astro-rdpcjfuz a__char--1"> 1 </span> </span> <span class="a__stripes astro-rdpcjfuz"></span> <span class="a__code js-code astro-rdpcjfuz"> <span class="a__char js-char astro-rdpcjfuz a__char--0"> 1 </span><span class="a__char js-char astro-rdpcjfuz a__char--0"> 1 </span><span class="a__char js-char astro-rdpcjfuz a__char--0"> 0 </span><span class="a__char js-char astro-rdpcjfuz a__char--1"> 0 </span><span class="a__char js-char astro-rdpcjfuz a__char--1"> 0 </span><span class="a__char js-char astro-rdpcjfuz a__char--0"> 1 </span><span class="a__char js-char astro-rdpcjfuz a__char--1"> 1 </span><span class="a__char a__char--blank js-char astro-rdpcjfuz"> </span><span class="a__char js-char astro-rdpcjfuz a__char--1"> 1 </span><span class="a__char js-char astro-rdpcjfuz a__char--0"> 1 </span><span class="a__char js-char astro-rdpcjfuz a__char--0"> 0 </span><span class="a__char js-char astro-rdpcjfuz a__char--1"> 1 </span><span class="a__char js-char astro-rdpcjfuz a__char--0"> 0 </span><span class="a__char js-char astro-rdpcjfuz a__char--0"> 1 </span><span class="a__char js-char astro-rdpcjfuz a__char--0"> 1 </span> </span> <span class="a__stripes astro-rdpcjfuz"></span> </span> <span class="a__triangle astro-rdpcjfuz"></span> </a-separator> <h1 class="s__title astro-7dteeuzc"> <span class="s__title__word js-word astro-7dteeuzc" style="null"><div class="word" style="position:relative;display:inline-block;"><div class="char char--C" style="position:relative;display:inline-block;"><span class="char__inner" data-letter="C">C</span></div><div class="char char--r" style="position:relative;display:inline-block;"><span class="char__inner" data-letter="R">r</span></div><div class="char char--e" style="position:relative;display:inline-block;"><span class="char__inner" data-letter="E">e</span></div><div class="char char--a" style="position:relative;display:inline-block;"><span class="char__inner" data-letter="A">a</span></div><div class="char char--t" style="position:relative;display:inline-block;"><span class="char__inner" data-letter="T">t</span></div><div class="char char--i" style="position:relative;display:inline-block;"><span class="char__inner" data-letter="I">i</span></div><div class="char char--v" style="position:relative;display:inline-block;"><span class="char__inner" data-letter="V">v</span></div><div class="char char--e" style="position:relative;display:inline-block;"><span class="char__inner" data-letter="E">e</span></div></div></span> <img alt="" class="s__title__asset js-star astro-7dteeuzc" height="48" src="/images/asset-star.svg" style="translate: none; rotate: none; scale: none; transform: translate(0px, 0px);" width="48"/> <span class="s__title__word js-word astro-7dteeuzc" style="null"><div class="word" style="position:relative;display:inline-block;"><div class="char char--D" style="position:relative;display:inline-block;"><span class="char__inner" data-letter="D">D</span></div><div class="char char--e" style="position:relative;display:inline-block;"><span class="char__inner" data-letter="E">e</span></div><div class="char char--v" style="position:relative;display:inline-block;"><span class="char__inner" data-letter="V">v</span></div><div class="char char--e" style="position:relative;display:inline-block;"><span class="char__inner" data-letter="E">e</span></div><div class="char char--l" style="position:relative;display:inline-block;"><span class="char__inner" data-letter="L">l</span></div><div class="char char--o" style="position:relative;display:inline-block;"><span class="char__inner" data-letter="O">o</span></div><div class="char char--p" style="position:relative;display:inline-block;"><span class="char__inner" data-letter="P">p</span></div><div class="char char--e" style="position:relative;display:inline-block;"><span class="char__inner" data-letter="E">e</span></div><div class="char char--r" style="position:relative;display:inline-block;"><span class="char__inner" data-letter="R">r</span></div></div></span> </h1> <a-separator class="style--primary s__separator js-separator astro-7dteeuzc astro-rdpcjfuz is-in-view" data-intersect="" style="translate: none; rotate: none; scale: none; transform: translate(0px, 0%);"> <span class="a__triangle astro-rdpcjfuz"></span> <span class="a__binaries astro-rdpcjfuz"> <span class="a__code js-code astro-rdpcjfuz"> <span class="a__char js-char astro-rdpcjfuz a__char--1"> 1 </span><span class="a__char js-char astro-rdpcjfuz a__char--1"> 0 </span><span class="a__char js-char astro-rdpcjfuz a__char--1"> 0 </span><span class="a__char js-char astro-rdpcjfuz a__char--0"> 0 </span><span class="a__char js-char astro-rdpcjfuz a__char--1"> 1 </span><span class="a__char js-char astro-rdpcjfuz a__char--1"> 0 </span><span class="a__char js-char astro-rdpcjfuz a__char--1"> 0 </span><span class="a__char a__char--blank js-char astro-rdpcjfuz"> </span><span class="a__char js-char astro-rdpcjfuz a__char--1"> 1 </span><span class="a__char js-char astro-rdpcjfuz a__char--1"> 1 </span><span class="a__char js-char astro-rdpcjfuz a__char--1"> 0 </span><span class="a__char js-char astro-rdpcjfuz a__char--1"> 1 </span><span class="a__char js-char astro-rdpcjfuz a__char--1"> 1 </span><span class="a__char js-char astro-rdpcjfuz a__char--0"> 1 </span><span class="a__char js-char astro-rdpcjfuz a__char--1"> 1 </span> </span> <span class="a__stripes astro-rdpcjfuz"></span> <span class="a__code js-code astro-rdpcjfuz"> <span class="a__char js-char astro-rdpcjfuz a__char--0"> 1 </span><span class="a__char js-char astro-rdpcjfuz a__char--1"> 0 </span><span class="a__char js-char astro-rdpcjfuz a__char--1"> 1 </span><span class="a__char js-char astro-rdpcjfuz a__char--1"> 0 </span><span class="a__char js-char astro-rdpcjfuz a__char--0"> 1 </span><span class="a__char js-char astro-rdpcjfuz a__char--1"> 0 </span><span class="a__char js-char astro-rdpcjfuz a__char--0"> 0 </span><span class="a__char a__char--blank js-char astro-rdpcjfuz"> </span><span class="a__char js-char astro-rdpcjfuz a__char--1"> 1 </span><span class="a__char js-char astro-rdpcjfuz a__char--1"> 1 </span><span class="a__char js-char astro-rdpcjfuz a__char--0"> 0 </span><span class="a__char js-char astro-rdpcjfuz a__char--1"> 1 </span><span class="a__char js-char astro-rdpcjfuz a__char--0"> 0 </span><span class="a__char js-char astro-rdpcjfuz a__char--0"> 0 </span><span class="a__char js-char astro-rdpcjfuz a__char--0"> 0 </span><span class="a__char a__char--blank js-char astro-rdpcjfuz"> </span><span class="a__char js-char astro-rdpcjfuz a__char--1"> 1 </span><span class="a__char js-char astro-rdpcjfuz a__char--1"> 1 </span><span class="a__char js-char astro-rdpcjfuz a__char--0"> 0 </span><span class="a__char js-char astro-rdpcjfuz a__char--0"> 1 </span><span class="a__char js-char astro-rdpcjfuz a__char--0"> 0 </span><span class="a__char js-char astro-rdpcjfuz a__char--0"> 0 </span><span class="a__char js-char astro-rdpcjfuz a__char--0"> 1 </span><span class="a__char a__char--blank js-char astro-rdpcjfuz"> </span><span class="a__char js-char astro-rdpcjfuz a__char--1"> 1 </span><span class="a__char js-char astro-rdpcjfuz a__char--0"> 1 </span><span class="a__char js-char astro-rdpcjfuz a__char--1"> 0 </span><span class="a__char js-char astro-rdpcjfuz a__char--0"> 1 </span><span class="a__char js-char astro-rdpcjfuz a__char--1"> 1 </span><span class="a__char js-char astro-rdpcjfuz a__char--0"> 1 </span><span class="a__char js-char astro-rdpcjfuz a__char--0"> 0 </span><span class="a__char a__char--blank js-char astro-rdpcjfuz"> </span><span class="a__char js-char astro-rdpcjfuz a__char--1"> 1 </span><span class="a__char js-char astro-rdpcjfuz a__char--1"> 1 </span><span class="a__char js-char astro-rdpcjfuz a__char--1"> 0 </span><span class="a__char js-char astro-rdpcjfuz a__char--0"> 0 </span><span class="a__char js-char astro-rdpcjfuz a__char--1"> 1 </span><span class="a__char js-char astro-rdpcjfuz a__char--1"> 1 </span><span class="a__char js-char astro-rdpcjfuz a__char--1"> 1 </span><span class="a__char a__char--blank js-char astro-rdpcjfuz"> </span><span class="a__char js-char astro-rdpcjfuz a__char--1"> 1 </span><span class="a__char js-char astro-rdpcjfuz a__char--1"> 1 </span><span class="a__char js-char astro-rdpcjfuz a__char--1"> 1 </span><span class="a__char js-char astro-rdpcjfuz a__char--1"> 0 </span><span class="a__char js-char astro-rdpcjfuz a__char--0"> 0 </span><span class="a__char js-char astro-rdpcjfuz a__char--1"> 1 </span><span class="a__char js-char astro-rdpcjfuz a__char--0"> 1 </span> </span> <span class="a__stripes astro-rdpcjfuz"></span> <span class="a__code js-code astro-rdpcjfuz"> <span class="a__char js-char astro-rdpcjfuz a__char--1"> 1 </span><span class="a__char js-char astro-rdpcjfuz a__char--1"> 0 </span><span class="a__char js-char astro-rdpcjfuz a__char--0"> 1 </span><span class="a__char js-char astro-rdpcjfuz a__char--0"> 1 </span><span class="a__char js-char astro-rdpcjfuz a__char--0"> 0 </span><span class="a__char js-char astro-rdpcjfuz a__char--0"> 0 </span><span class="a__char js-char astro-rdpcjfuz a__char--0"> 1 </span><span class="a__char a__char--blank js-char astro-rdpcjfuz"> </span><span class="a__char js-char astro-rdpcjfuz a__char--1"> 1 </span><span class="a__char js-char astro-rdpcjfuz a__char--0"> 1 </span><span class="a__char js-char astro-rdpcjfuz a__char--0"> 0 </span><span class="a__char js-char astro-rdpcjfuz a__char--1"> 1 </span><span class="a__char js-char astro-rdpcjfuz a__char--0"> 1 </span><span class="a__char js-char astro-rdpcjfuz a__char--1"> 1 </span><span class="a__char js-char astro-rdpcjfuz a__char--0"> 1 </span><span class="a__char a__char--blank js-char astro-rdpcjfuz"> </span><span class="a__char js-char astro-rdpcjfuz a__char--1"> 1 </span><span class="a__char js-char astro-rdpcjfuz a__char--0"> 1 </span><span class="a__char js-char astro-rdpcjfuz a__char--1"> 1 </span><span class="a__char js-char astro-rdpcjfuz a__char--0"> 0 </span><span class="a__char js-char astro-rdpcjfuz a__char--0"> 1 </span><span class="a__char js-char astro-rdpcjfuz a__char--1"> 0 </span><span class="a__char js-char astro-rdpcjfuz a__char--1"> 1 </span><span class="a__char a__char--blank js-char astro-rdpcjfuz"> </span><span class="a__char js-char astro-rdpcjfuz a__char--1"> 1 </span><span class="a__char js-char astro-rdpcjfuz a__char--0"> 1 </span><span class="a__char js-char astro-rdpcjfuz a__char--1"> 1 </span><span class="a__char js-char astro-rdpcjfuz a__char--0"> 0 </span><span class="a__char js-char astro-rdpcjfuz a__char--0"> 0 </span><span class="a__char js-char astro-rdpcjfuz a__char--1"> 1 </span><span class="a__char js-char astro-rdpcjfuz a__char--0"> 0 </span> </span> <span class="a__stripes astro-rdpcjfuz"></span> <span class="a__code js-code astro-rdpcjfuz"> <span class="a__char js-char astro-rdpcjfuz a__char--1"> 1 </span><span class="a__char js-char astro-rdpcjfuz a__char--0"> 0 </span><span class="a__char js-char astro-rdpcjfuz a__char--0"> 1 </span><span class="a__char js-char astro-rdpcjfuz a__char--1"> 0 </span><span class="a__char js-char astro-rdpcjfuz a__char--1"> 1 </span><span class="a__char js-char astro-rdpcjfuz a__char--0"> 1 </span><span class="a__char js-char astro-rdpcjfuz a__char--0"> 1 </span><span class="a__char a__char--blank js-char astro-rdpcjfuz"> </span><span class="a__char js-char astro-rdpcjfuz a__char--0"> 1 </span><span class="a__char js-char astro-rdpcjfuz a__char--1"> 1 </span><span class="a__char js-char astro-rdpcjfuz a__char--0"> 0 </span><span class="a__char js-char astro-rdpcjfuz a__char--1"> 0 </span><span class="a__char js-char astro-rdpcjfuz a__char--1"> 0 </span><span class="a__char js-char astro-rdpcjfuz a__char--1"> 0 </span><span class="a__char js-char astro-rdpcjfuz a__char--1"> 1 </span><span class="a__char a__char--blank js-char astro-rdpcjfuz"> </span><span class="a__char js-char astro-rdpcjfuz a__char--1"> 1 </span><span class="a__char js-char astro-rdpcjfuz a__char--1"> 1 </span><span class="a__char js-char astro-rdpcjfuz a__char--0"> 1 </span><span class="a__char js-char astro-rdpcjfuz a__char--1"> 1 </span><span class="a__char js-char astro-rdpcjfuz a__char--0"> 0 </span><span class="a__char js-char astro-rdpcjfuz a__char--0"> 0 </span><span class="a__char js-char astro-rdpcjfuz a__char--0"> 1 </span> </span> <span class="a__stripes astro-rdpcjfuz"></span> </span> <span class="a__triangle astro-rdpcjfuz"></span> </a-separator> </div> <div class="s__border js-border astro-7dteeuzc" style="translate: none; rotate: none; scale: none; transform: translate(0px, 0px);"></div> </div><!-- .s-hero --> <section class="s-about astro-am7g2yfn is-in-view" data-intersect="" id="about" style="--offset-y: -399.95241979336595px;"> <div class="s__inner js-inner astro-am7g2yfn"> <div class="s__block s__block--about astro-am7g2yfn"> <h2 class="s__title astro-am7g2yfn">About</h2> <div class="s__content astro-am7g2yfn"> <p class="astro-am7g2yfn">
I collaborate with agencies and designers to craft memorable user experiences, bringing their vision to life with a nice touch of animation.
</p> <p class="astro-am7g2yfn">
I started with Dreamweaver, played with Flash and ActionScript, did
          back-end dev from scratch, worked with all kinds of CMS, focused on
          creative dev, worked on 140+ projects, led a team of 10 designers and
          developers, founded <a class="astro-am7g2yfn" href="https://waaark.com" rel="noreferrer" target="_blank">
Waaark
</a> and <a class="astro-am7g2yfn" href="https://incredibles.dev" rel="noreferrer" target="_blank">
incredibles.dev
</a>, won a few awards, and keep on learning.
</p> </div><!-- .s__content --> </div><!-- .s__block --> <div class="s__block s__block--awards astro-am7g2yfn"> <h2 class="s__title astro-am7g2yfn">Awards</h2> <ul class="s__awards astro-am7g2yfn"> <li class="s__award s__award--counter s__award--awwwards js-award astro-am7g2yfn"> <span class="s__award__inner astro-am7g2yfn"> <span class="s__award__name astro-am7g2yfn">awwwards</span> <span class="s__award__counter astro-am7g2yfn">(SOTD x 16)</span><span class="s__award__counter astro-am7g2yfn">(Honors x 1)</span> </span> <span class="s__award__mask astro-am7g2yfn"></span> </li><li class="s__award s__award--counter s__award--fwa js-award astro-am7g2yfn"> <span class="s__award__inner astro-am7g2yfn"> <span class="s__award__name astro-am7g2yfn">fwa</span> <span class="s__award__counter astro-am7g2yfn">(SOTD x 4)</span><span class="s__award__counter astro-am7g2yfn">(MOTD x 2)</span> </span> <span class="s__award__mask astro-am7g2yfn"></span> </li><li class="s__award s__award--counter s__award--cssda js-award astro-am7g2yfn"> <span class="s__award__inner astro-am7g2yfn"> <span class="s__award__name astro-am7g2yfn">cssda</span> <span class="s__award__counter astro-am7g2yfn">(WOTD x 18)</span><span class="s__award__counter astro-am7g2yfn">(WOTM x 1)</span> </span> <span class="s__award__mask astro-am7g2yfn"></span> </li> <li class="s__award s__award--text s__award--webby2025 js-award astro-am7g2yfn"> <span class="s__award__inner astro-am7g2yfn"> <span class="s__award__text astro-am7g2yfn">2025 Webby Awards Winner <br/>Best Home Page</span> </span> <span class="s__award__mask astro-am7g2yfn"></span> </li><li class="s__award s__award--text s__award--commArt2017 js-award astro-am7g2yfn"> <span class="s__award__inner astro-am7g2yfn"> <span class="s__award__text astro-am7g2yfn">Comm Arts Mag Interactive  Annual Competition Winner 2017</span> </span> <span class="s__award__mask astro-am7g2yfn"></span> </li><li class="s__award s__award--text s__award--netMag2016 js-award astro-am7g2yfn"> <span class="s__award__inner astro-am7g2yfn"> <span class="s__award__text astro-am7g2yfn">Net Mag SOTM Summer 2016</span> </span> <span class="s__award__mask astro-am7g2yfn"></span> </li><li class="s__award s__award--text s__award--gsapOct2024 js-award astro-am7g2yfn"> <span class="s__award__inner astro-am7g2yfn"> <span class="s__award__text astro-am7g2yfn">GSAP SOTM October 2024</span> </span> <span class="s__award__mask astro-am7g2yfn"></span> </li><li class="s__award s__award--text s__award--gsapNov2024 js-award astro-am7g2yfn"> <span class="s__award__inner astro-am7g2yfn"> <span class="s__award__text astro-am7g2yfn">GSAP SOTM November 2024</span> </span> <span class="s__award__mask astro-am7g2yfn"></span> </li><li class="s__award s__award--text s__award--CSSDA2016 js-award astro-am7g2yfn"> <span class="s__award__inner astro-am7g2yfn"> <span class="s__award__text astro-am7g2yfn">CSSDA Best Front-End <br/>Developer 2016</span> </span> <span class="s__award__mask astro-am7g2yfn"></span> </li><li class="s__award s__award--text s__award--CSSDA2015 js-award astro-am7g2yfn"> <span class="s__award__inner astro-am7g2yfn"> <span class="s__award__text astro-am7g2yfn">CSSDA Best Front-End <br/>Developer 2015</span> </span> <span class="s__award__mask astro-am7g2yfn"></span> </li> <li class="s__award s__award--blank astro-am7g2yfn"> <svg class="astro-am7g2yfn" fill="none" height="113" viewbox="0 0 101 113" width="101" xmlns="http://www.w3.org/2000/svg"> <path class="astro-am7g2yfn" d="M80.05 107.398c-21.944 12.67-52.875.18-69.085-27.896m69.086 27.896c21.945-12.67 26.594-45.702 10.384-73.778M80.05 107.398c-19.686 11.366-48.786-2.181-64.996-30.258C-1.155 49.063 1.663 17.09 21.35 5.723m58.702 101.675c19.686-11.366 22.504-43.34 6.293-71.417C70.134 7.904 41.034-5.642 21.35 5.723m58.702 101.675c-10.973 6.335-33.009-11.29-49.219-39.367-16.21-28.077-20.456-55.973-9.483-62.308m58.702 101.675c10.972-6.335 6.726-34.231-9.484-62.308C54.357 17.013 32.322-.612 21.35 5.723m58.702 101.675c3.74-2.159-6.262-26.77-22.456-54.818S25.089 3.564 21.349 5.723m58.702 101.675c-3.74 2.159-20.053-18.808-36.246-46.856C27.61 32.493 17.609 7.882 21.349 5.723M10.965 79.502C-5.245 51.425-.596 18.393 21.349 5.723M10.965 79.502c11.366 19.686 38.37 25.373 60.314 12.703 21.946-12.67 30.522-38.9 19.156-58.585m-79.47 45.882C-.401 59.816 8.175 33.586 30.121 20.916c21.945-12.67 48.948-6.982 60.314 12.704m-79.47 45.882c6.335 10.972 29.26 9.596 51.206-3.074C84.115 63.758 96.77 44.592 90.434 33.62m-79.47 45.882C4.63 68.529 17.285 49.363 39.23 36.693c21.945-12.67 44.87-14.046 51.205-3.073M21.349 5.723c21.945-12.67 52.876-.18 69.086 27.897M54.749 63.573C32.778 76.258 13.192 83.358 11.033 79.618c-2.16-3.74 13.783-17.15 35.754-29.836 21.97-12.685 41.557-19.785 43.716-16.045 2.159 3.74-13.783 17.15-35.754 29.836Z"></path> </svg> <svg class="astro-am7g2yfn" fill="none" height="49" viewbox="0 0 49 49" width="49" xmlns="http://www.w3.org/2000/svg"> <path class="astro-am7g2yfn" d="m24.5 0 3.3 21.2L49 24.5l-21.2 3.3L24.5 49l-3.3-21.2L0 24.5l21.2-3.3L24.5 0z" fill="#160000"></path> </svg> <svg class="astro-am7g2yfn" fill="none" height="49" viewbox="0 0 49 49" width="49" xmlns="http://www.w3.org/2000/svg"> <path class="astro-am7g2yfn" d="m24.5 0 3.3 21.2L49 24.5l-21.2 3.3L24.5 49l-3.3-21.2L0 24.5l21.2-3.3L24.5 0z" fill="#160000"></path> </svg> <svg class="astro-am7g2yfn" fill="none" height="49" viewbox="0 0 49 49" width="49" xmlns="http://www.w3.org/2000/svg"> <path class="astro-am7g2yfn" d="m24.5 0 3.3 21.2L49 24.5l-21.2 3.3L24.5 49l-3.3-21.2L0 24.5l21.2-3.3L24.5 0z" fill="#160000"></path> </svg> <svg class="astro-am7g2yfn" fill="none" height="49" viewbox="0 0 49 49" width="49" xmlns="http://www.w3.org/2000/svg"> <path class="astro-am7g2yfn" d="m24.5 0 3.3 21.2L49 24.5l-21.2 3.3L24.5 49l-3.3-21.2L0 24.5l21.2-3.3L24.5 0z" fill="#160000"></path> </svg> </li> </ul><!-- .s__content --> </div><!-- .s__block --> </div><!-- .s__inner --> <svg class="s__grid js-grid astro-am7g2yfn" style="width: 1248px; height: 2958px;"> <path class="js-path astro-am7g2yfn" d=""></path> </svg> <canvas class="s__canvas js-canvas astro-am7g2yfn" height="2958" width="1248"></canvas> </section> <a-separator class="style--secondary astro-j7pv25f6 astro-rdpcjfuz is-out-of-view is-out-of-view-bottom" data-intersect=""> <span class="a__triangle astro-rdpcjfuz"></span> <span class="a__binaries astro-rdpcjfuz"> <span class="a__code js-code astro-rdpcjfuz"> <span class="a__char a__char--1 js-char astro-rdpcjfuz"> 1 </span><span class="a__char a__char--0 js-char astro-rdpcjfuz"> 0 </span><span class="a__char a__char--0 js-char astro-rdpcjfuz"> 0 </span><span class="a__char a__char--0 js-char astro-rdpcjfuz"> 0 </span><span class="a__char a__char--1 js-char astro-rdpcjfuz"> 1 </span><span class="a__char a__char--0 js-char astro-rdpcjfuz"> 0 </span><span class="a__char a__char--0 js-char astro-rdpcjfuz"> 0 </span><span class="a__char a__char--blank js-char astro-rdpcjfuz"> </span><span class="a__char a__char--1 js-char astro-rdpcjfuz"> 1 </span><span class="a__char a__char--1 js-char astro-rdpcjfuz"> 1 </span><span class="a__char a__char--0 js-char astro-rdpcjfuz"> 0 </span><span class="a__char a__char--1 js-char astro-rdpcjfuz"> 1 </span><span class="a__char a__char--1 js-char astro-rdpcjfuz"> 1 </span><span class="a__char a__char--1 js-char astro-rdpcjfuz"> 1 </span><span class="a__char a__char--1 js-char astro-rdpcjfuz"> 1 </span> </span> <span class="a__stripes astro-rdpcjfuz"></span> <span class="a__code js-code astro-rdpcjfuz"> <span class="a__char a__char--1 js-char astro-rdpcjfuz"> 1 </span><span class="a__char a__char--0 js-char astro-rdpcjfuz"> 0 </span><span class="a__char a__char--1 js-char astro-rdpcjfuz"> 1 </span><span class="a__char a__char--0 js-char astro-rdpcjfuz"> 0 </span><span class="a__char a__char--1 js-char astro-rdpcjfuz"> 1 </span><span class="a__char a__char--0 js-char astro-rdpcjfuz"> 0 </span><span class="a__char a__char--0 js-char astro-rdpcjfuz"> 0 </span><span class="a__char a__char--blank js-char astro-rdpcjfuz"> </span><span class="a__char a__char--1 js-char astro-rdpcjfuz"> 1 </span><span class="a__char a__char--1 js-char astro-rdpcjfuz"> 1 </span><span class="a__char a__char--0 js-char astro-rdpcjfuz"> 0 </span><span class="a__char a__char--1 js-char astro-rdpcjfuz"> 1 </span><span class="a__char a__char--0 js-char astro-rdpcjfuz"> 0 </span><span class="a__char a__char--0 js-char astro-rdpcjfuz"> 0 </span><span class="a__char a__char--0 js-char astro-rdpcjfuz"> 0 </span><span class="a__char a__char--blank js-char astro-rdpcjfuz"> </span><span class="a__char a__char--1 js-char astro-rdpcjfuz"> 1 </span><span class="a__char a__char--1 js-char astro-rdpcjfuz"> 1 </span><span class="a__char a__char--0 js-char astro-rdpcjfuz"> 0 </span><span class="a__char a__char--1 js-char astro-rdpcjfuz"> 1 </span><span class="a__char a__char--0 js-char astro-rdpcjfuz"> 0 </span><span class="a__char a__char--0 js-char astro-rdpcjfuz"> 0 </span><span class="a__char a__char--1 js-char astro-rdpcjfuz"> 1 </span><span class="a__char a__char--blank js-char astro-rdpcjfuz"> </span><span class="a__char a__char--1 js-char astro-rdpcjfuz"> 1 </span><span class="a__char a__char--1 js-char astro-rdpcjfuz"> 1 </span><span class="a__char a__char--0 js-char astro-rdpcjfuz"> 0 </span><span class="a__char a__char--1 js-char astro-rdpcjfuz"> 1 </span><span class="a__char a__char--1 js-char astro-rdpcjfuz"> 1 </span><span class="a__char a__char--1 js-char astro-rdpcjfuz"> 1 </span><span class="a__char a__char--0 js-char astro-rdpcjfuz"> 0 </span><span class="a__char a__char--blank js-char astro-rdpcjfuz"> </span><span class="a__char a__char--1 js-char astro-rdpcjfuz"> 1 </span><span class="a__char a__char--1 js-char astro-rdpcjfuz"> 1 </span><span class="a__char a__char--0 js-char astro-rdpcjfuz"> 0 </span><span class="a__char a__char--0 js-char astro-rdpcjfuz"> 0 </span><span class="a__char a__char--1 js-char astro-rdpcjfuz"> 1 </span><span class="a__char a__char--1 js-char astro-rdpcjfuz"> 1 </span><span class="a__char a__char--1 js-char astro-rdpcjfuz"> 1 </span><span class="a__char a__char--blank js-char astro-rdpcjfuz"> </span><span class="a__char a__char--1 js-char astro-rdpcjfuz"> 1 </span><span class="a__char a__char--1 js-char astro-rdpcjfuz"> 1 </span><span class="a__char a__char--1 js-char astro-rdpcjfuz"> 1 </span><span class="a__char a__char--0 js-char astro-rdpcjfuz"> 0 </span><span class="a__char a__char--0 js-char astro-rdpcjfuz"> 0 </span><span class="a__char a__char--1 js-char astro-rdpcjfuz"> 1 </span><span class="a__char a__char--1 js-char astro-rdpcjfuz"> 1 </span> </span> <span class="a__stripes astro-rdpcjfuz"></span> <span class="a__code js-code astro-rdpcjfuz"> <span class="a__char a__char--1 js-char astro-rdpcjfuz"> 1 </span><span class="a__char a__char--0 js-char astro-rdpcjfuz"> 0 </span><span class="a__char a__char--1 js-char astro-rdpcjfuz"> 1 </span><span class="a__char a__char--1 js-char astro-rdpcjfuz"> 1 </span><span class="a__char a__char--0 js-char astro-rdpcjfuz"> 0 </span><span class="a__char a__char--0 js-char astro-rdpcjfuz"> 0 </span><span class="a__char a__char--1 js-char astro-rdpcjfuz"> 1 </span><span class="a__char a__char--blank js-char astro-rdpcjfuz"> </span><span class="a__char a__char--1 js-char astro-rdpcjfuz"> 1 </span><span class="a__char a__char--1 js-char astro-rdpcjfuz"> 1 </span><span class="a__char a__char--0 js-char astro-rdpcjfuz"> 0 </span><span class="a__char a__char--1 js-char astro-rdpcjfuz"> 1 </span><span class="a__char a__char--1 js-char astro-rdpcjfuz"> 1 </span><span class="a__char a__char--1 js-char astro-rdpcjfuz"> 1 </span><span class="a__char a__char--1 js-char astro-rdpcjfuz"> 1 </span><span class="a__char a__char--blank js-char astro-rdpcjfuz"> </span><span class="a__char a__char--1 js-char astro-rdpcjfuz"> 1 </span><span class="a__char a__char--1 js-char astro-rdpcjfuz"> 1 </span><span class="a__char a__char--1 js-char astro-rdpcjfuz"> 1 </span><span class="a__char a__char--0 js-char astro-rdpcjfuz"> 0 </span><span class="a__char a__char--1 js-char astro-rdpcjfuz"> 1 </span><span class="a__char a__char--0 js-char astro-rdpcjfuz"> 0 </span><span class="a__char a__char--1 js-char astro-rdpcjfuz"> 1 </span><span class="a__char a__char--blank js-char astro-rdpcjfuz"> </span><span class="a__char a__char--1 js-char astro-rdpcjfuz"> 1 </span><span class="a__char a__char--1 js-char astro-rdpcjfuz"> 1 </span><span class="a__char a__char--1 js-char astro-rdpcjfuz"> 1 </span><span class="a__char a__char--0 js-char astro-rdpcjfuz"> 0 </span><span class="a__char a__char--0 js-char astro-rdpcjfuz"> 0 </span><span class="a__char a__char--1 js-char astro-rdpcjfuz"> 1 </span><span class="a__char a__char--0 js-char astro-rdpcjfuz"> 0 </span> </span> <span class="a__stripes astro-rdpcjfuz"></span> <span class="a__code js-code astro-rdpcjfuz"> <span class="a__char a__char--1 js-char astro-rdpcjfuz"> 1 </span><span class="a__char a__char--0 js-char astro-rdpcjfuz"> 0 </span><span class="a__char a__char--1 js-char astro-rdpcjfuz"> 1 </span><span class="a__char a__char--0 js-char astro-rdpcjfuz"> 0 </span><span class="a__char a__char--1 js-char astro-rdpcjfuz"> 1 </span><span class="a__char a__char--1 js-char astro-rdpcjfuz"> 1 </span><span class="a__char a__char--1 js-char astro-rdpcjfuz"> 1 </span><span class="a__char a__char--blank js-char astro-rdpcjfuz"> </span><span class="a__char a__char--1 js-char astro-rdpcjfuz"> 1 </span><span class="a__char a__char--1 js-char astro-rdpcjfuz"> 1 </span><span class="a__char a__char--0 js-char astro-rdpcjfuz"> 0 </span><span class="a__char a__char--0 js-char astro-rdpcjfuz"> 0 </span><span class="a__char a__char--0 js-char astro-rdpcjfuz"> 0 </span><span class="a__char a__char--0 js-char astro-rdpcjfuz"> 0 </span><span class="a__char a__char--1 js-char astro-rdpcjfuz"> 1 </span><span class="a__char a__char--blank js-char astro-rdpcjfuz"> </span><span class="a__char a__char--1 js-char astro-rdpcjfuz"> 1 </span><span class="a__char a__char--1 js-char astro-rdpcjfuz"> 1 </span><span class="a__char a__char--1 js-char astro-rdpcjfuz"> 1 </span><span class="a__char a__char--1 js-char astro-rdpcjfuz"> 1 </span><span class="a__char a__char--0 js-char astro-rdpcjfuz"> 0 </span><span class="a__char a__char--0 js-char astro-rdpcjfuz"> 0 </span><span class="a__char a__char--1 js-char astro-rdpcjfuz"> 1 </span> </span> <span class="a__stripes astro-rdpcjfuz"></span> </span> <span class="a__triangle astro-rdpcjfuz"></span> </a-separator> <section class="s-work astro-doefkd43 is-out-of-view is-out-of-view-bottom" data-intersect="" id="work" style="--height: 1650lvh;"> <div class="s__outer astro-doefkd43"> <div class="s__inner js-container astro-doefkd43" style="clip-path: inset(0px 1rem);"> <h2 class="s__title astro-doefkd43"> <span class="s__title__inner js-title astro-doefkd43"> <span class="s__title__letter js-letter astro-doefkd43">W</span> <span class="s__title__letter js-letter astro-doefkd43">O</span> <span class="s__title__letter js-letter astro-doefkd43">R</span> <span class="s__title__letter js-letter astro-doefkd43">K</span> </span> </h2> <div class="s__scene js-scene astro-doefkd43" style="translate: none; rotate: none; scale: none; transform: scale(0.75, 0.75);"> <a-work class="s__scene__work s__scene__work--video js-work astro-meqjtcea" progress="1" style="--size: 0.8016159561676823; --y: 0.5683764391272645; --progress: 1;"> <div class="a__inner astro-meqjtcea"> <a class="astro-meqjtcea" href="https://www.agence-s.fr/" target="_blank"> <video class="a__video js-video astro-meqjtcea" data-src="/_astro/Agences-Work.CcLpoyOs.mp4" height="636" loop="" muted="" playsinline="" width="1082"></video> <div class="a__caption astro-meqjtcea"> <div class="a__caption__text astro-meqjtcea"> Agences </div><!-- .a__caption__text --> <div class="a__caption__key astro-meqjtcea">
#e0cb-0000/33 </div><!-- .a__caption__key --> </div><!-- .a__caption --> </a> </div><!-- .a__inner --> </a-work> <a-work class="s__scene__work s__scene__work--video js-work astro-meqjtcea" progress="1" style="--size: 0.7492408023567438; --y: -0.5469731848158466; --progress: 1;"> <div class="a__inner astro-meqjtcea"> <a class="astro-meqjtcea" href="https://nodcoding.com" target="_blank"> <video class="a__video js-video astro-meqjtcea" data-src="/_astro/Nod-Transition.Cgjsr7Jp.mp4" height="636" loop="" muted="" playsinline="" width="1082"></video> <div class="a__caption astro-meqjtcea"> <div class="a__caption__text astro-meqjtcea"> Nod Coding Bootcamp </div><!-- .a__caption__text --> <div class="a__caption__key astro-meqjtcea">
#yfg4-0001/33 </div><!-- .a__caption__key --> </div><!-- .a__caption --> </a> </div><!-- .a__inner --> </a-work> <a-work class="s__scene__work s__scene__work--video js-work astro-meqjtcea" progress="1" style="--size: 0.7572364146225716; --y: 0.8861032347410713; --progress: 1;"> <div class="a__inner astro-meqjtcea"> <a class="astro-meqjtcea" href="https://incredibles.dev" target="_blank"> <video class="a__video js-video astro-meqjtcea" data-src="/_astro/Incredibles-Scroll.C2FsGtmZ.mp4" height="636" loop="" muted="" playsinline="" width="1082"></video> <div class="a__caption astro-meqjtcea"> <div class="a__caption__text astro-meqjtcea"> incredibles </div><!-- .a__caption__text --> <div class="a__caption__key astro-meqjtcea">
#28pk-0002/33 </div><!-- .a__caption__key --> </div><!-- .a__caption --> </a> </div><!-- .a__inner --> </a-work> <a-work class="s__scene__work s__scene__work--video js-work astro-meqjtcea" progress="1" style="--size: 0.9247619284737192; --y: -0.9338176107925674; --progress: 1;"> <div class="a__inner astro-meqjtcea"> <a class="astro-meqjtcea" href="https://www.desiderecrutement.com/en/" target="_blank"> <video class="a__video js-video astro-meqjtcea" data-src="/_astro/Deside-Site.CmhdHL0t.mp4" height="636" loop="" muted="" playsinline="" width="1082"></video> <div class="a__caption astro-meqjtcea"> <div class="a__caption__text astro-meqjtcea"> Deside Recrutement </div><!-- .a__caption__text --> <div class="a__caption__key astro-meqjtcea">
#1iq7-0003/33 </div><!-- .a__caption__key --> </div><!-- .a__caption --> </a> </div><!-- .a__inner --> </a-work> <a-work class="s__scene__work s__scene__work--video js-work astro-meqjtcea" progress="1" style="--size: 0.8122182844055106; --y: 0.6983496631223226; --progress: 1;"> <div class="a__inner astro-meqjtcea"> <a class="astro-meqjtcea" href="https://duten.com" target="_blank"> <video class="a__video js-video astro-meqjtcea" data-src="/_astro/Duten-3D.CnijAj5p.mp4" height="636" loop="" muted="" playsinline="" width="1082"></video> <div class="a__caption astro-meqjtcea"> <div class="a__caption__text astro-meqjtcea"> Duten </div><!-- .a__caption__text --> <div class="a__caption__key astro-meqjtcea">
#o8e6-0004/33 </div><!-- .a__caption__key --> </div><!-- .a__caption --> </a> </div><!-- .a__inner --> </a-work> <a-work class="s__scene__work s__scene__work--video js-work astro-meqjtcea" progress="1" style="--size: 0.5071051414578066; --y: -0.7509332588006141; --progress: 1;"> <div class="a__inner astro-meqjtcea"> <a class="astro-meqjtcea" href="https://codepen.io/wodniack" target="_blank"> <video class="a__video js-video astro-meqjtcea" data-src="/_astro/Pen-4.BOCLYASq.mp4" height="636" loop="" muted="" playsinline="" width="1082"></video> <div class="a__caption astro-meqjtcea"> <div class="a__caption__text astro-meqjtcea"> CodePen </div><!-- .a__caption__text --> <div class="a__caption__key astro-meqjtcea">
#1nfh-0005/33 </div><!-- .a__caption__key --> </div><!-- .a__caption --> </a> </div><!-- .a__inner --> </a-work> <a-work class="s__scene__work s__scene__work--video js-work astro-meqjtcea" progress="1" style="--size: 0.8221971483857171; --y: 0.5712047221547495; --progress: 1;"> <div class="a__inner astro-meqjtcea"> <a class="astro-meqjtcea" href="#" target="_blank"> <video class="a__video js-video astro-meqjtcea" data-src="/_astro/Pt-Navigation.GbcOoFQt.mp4" height="636" loop="" muted="" playsinline="" width="1082"></video> <div class="a__caption astro-meqjtcea"> <div class="a__caption__text astro-meqjtcea"> Plantatami </div><!-- .a__caption__text --> <div class="a__caption__key astro-meqjtcea">
#62t7-0006/33 </div><!-- .a__caption__key --> </div><!-- .a__caption --> </a> </div><!-- .a__inner --> </a-work> <a-work class="s__scene__work s__scene__work--video js-work astro-meqjtcea" progress="1" style="--size: 0.940275274661384; --y: -0.7021633765240523; --progress: 1;"> <div class="a__inner astro-meqjtcea"> <a class="astro-meqjtcea" href="https://nodcoding.com" target="_blank"> <video class="a__video js-video astro-meqjtcea" data-src="/_astro/Nod-404.DvJptcy9.mp4" height="636" loop="" muted="" playsinline="" width="1082"></video> <div class="a__caption astro-meqjtcea"> <div class="a__caption__text astro-meqjtcea"> Nod Coding Bootcamp </div><!-- .a__caption__text --> <div class="a__caption__key astro-meqjtcea">
#8ov5-0007/33 </div><!-- .a__caption__key --> </div><!-- .a__caption --> </a> </div><!-- .a__inner --> </a-work> <a-work class="s__scene__work s__scene__work--video js-work astro-meqjtcea" progress="1" style="--size: 0.5536928543804926; --y: 0.8825201241177448; --progress: 1;"> <div class="a__inner astro-meqjtcea"> <a class="astro-meqjtcea" href="#" target="_blank"> <video class="a__video js-video astro-meqjtcea" data-src="/_astro/Hf-Navigation.Gkvt7Etn.mp4" height="636" loop="" muted="" playsinline="" width="1082"></video> <div class="a__caption astro-meqjtcea"> <div class="a__caption__text astro-meqjtcea"> Hunter Farmer </div><!-- .a__caption__text --> <div class="a__caption__key astro-meqjtcea">
#vfs4-0008/33 </div><!-- .a__caption__key --> </div><!-- .a__caption --> </a> </div><!-- .a__inner --> </a-work> <a-work class="s__scene__work s__scene__work--video js-work astro-meqjtcea" progress="1" style="--size: 0.8673081036355901; --y: -0.5153470318889948; --progress: 1;"> <div class="a__inner astro-meqjtcea"> <a class="astro-meqjtcea" href="https://generousbranding.com" target="_blank"> <video class="a__video js-video astro-meqjtcea" data-src="/_astro/Generous-Transition.Dysz98iA.mp4" height="636" loop="" muted="" playsinline="" width="1082"></video> <div class="a__caption astro-meqjtcea"> <div class="a__caption__text astro-meqjtcea"> Generous Branding </div><!-- .a__caption__text --> <div class="a__caption__key astro-meqjtcea">
#88u7-0009/33 </div><!-- .a__caption__key --> </div><!-- .a__caption --> </a> </div><!-- .a__inner --> </a-work> <a-work class="s__scene__work s__scene__work--video js-work astro-meqjtcea" progress="1" style="--size: 0.6174213606378944; --y: 0.5912957837531118; --progress: 1;"> <div class="a__inner astro-meqjtcea"> <a class="astro-meqjtcea" href="https://www.rudlundschwarm.at/" target="_blank"> <video class="a__video js-video astro-meqjtcea" data-src="/_astro/Rudl-Transition.CcgAkF6S.mp4" height="636" loop="" muted="" playsinline="" width="1082"></video> <div class="a__caption astro-meqjtcea"> <div class="a__caption__text astro-meqjtcea"> Rudl und Schwarm </div><!-- .a__caption__text --> <div class="a__caption__key astro-meqjtcea">
#5ez1-0010/33 </div><!-- .a__caption__key --> </div><!-- .a__caption --> </a> </div><!-- .a__inner --> </a-work> <a-work class="s__scene__work s__scene__work--video js-work astro-meqjtcea" progress="1" style="--size: 0.8358685209732679; --y: -0.7378253928735397; --progress: 1;"> <div class="a__inner astro-meqjtcea"> <a class="astro-meqjtcea" href="https://codepen.io/wodniack" target="_blank"> <video class="a__video js-video astro-meqjtcea" data-src="/_astro/Pen-5.CI9l33_j.mp4" height="636" loop="" muted="" playsinline="" width="1082"></video> <div class="a__caption astro-meqjtcea"> <div class="a__caption__text astro-meqjtcea"> CodePen </div><!-- .a__caption__text --> <div class="a__caption__key astro-meqjtcea">
#g8gv-0011/33 </div><!-- .a__caption__key --> </div><!-- .a__caption --> </a> </div><!-- .a__inner --> </a-work> <a-work class="s__scene__work s__scene__work--video js-work astro-meqjtcea" progress="1" style="--size: 0.9544474022986548; --y: 0.9684306678236965; --progress: 1;"> <div class="a__inner astro-meqjtcea"> <a class="astro-meqjtcea" href="https://duten.com" target="_blank"> <video class="a__video js-video astro-meqjtcea" data-src="/_astro/Duten-Fluid.t8C3AKXP.mp4" height="636" loop="" muted="" playsinline="" width="1082"></video> <div class="a__caption astro-meqjtcea"> <div class="a__caption__text astro-meqjtcea"> Duten </div><!-- .a__caption__text --> <div class="a__caption__key astro-meqjtcea">
#nc10-0012/33 </div><!-- .a__caption__key --> </div><!-- .a__caption --> </a> </div><!-- .a__inner --> </a-work> <a-work class="s__scene__work s__scene__work--video js-work astro-meqjtcea" progress="1" style="--size: 0.5257836604223168; --y: -0.5396726109703651; --progress: 1;"> <div class="a__inner astro-meqjtcea"> <a class="astro-meqjtcea" href="https://duten.com" target="_blank"> <video class="a__video js-video astro-meqjtcea" data-src="/_astro/Duten-Intro.CP3CW1O6.mp4" height="636" loop="" muted="" playsinline="" width="1082"></video> <div class="a__caption astro-meqjtcea"> <div class="a__caption__text astro-meqjtcea"> Duten </div><!-- .a__caption__text --> <div class="a__caption__key astro-meqjtcea">
#lq7l-0013/33 </div><!-- .a__caption__key --> </div><!-- .a__caption --> </a> </div><!-- .a__inner --> </a-work> <a-work class="s__scene__work s__scene__work--video js-work astro-meqjtcea" progress="1" style="--size: 0.5862102632815974; --y: 0.5387851101618819; --progress: 1;"> <div class="a__inner astro-meqjtcea"> <a class="astro-meqjtcea" href="https://qbitcapital.xyz" target="_blank"> <video class="a__video js-video astro-meqjtcea" data-src="/_astro/Qbit-Menu.DQBETGn4.mp4" height="636" loop="" muted="" playsinline="" width="1082"></video> <div class="a__caption astro-meqjtcea"> <div class="a__caption__text astro-meqjtcea"> QBIT Capital </div><!-- .a__caption__text --> <div class="a__caption__key astro-meqjtcea">
#kmd0-0014/33 </div><!-- .a__caption__key --> </div><!-- .a__caption --> </a> </div><!-- .a__inner --> </a-work> <a-work class="s__scene__work s__scene__work--video js-work astro-meqjtcea" progress="1" style="--size: 0.6748776078329294; --y: -0.8045725118453291; --progress: 1;"> <div class="a__inner astro-meqjtcea"> <a class="astro-meqjtcea" href="https://honorsociety.tv/" target="_blank"> <video class="a__video js-video astro-meqjtcea" data-src="/_astro/HS-Site.Br4spn2E.mp4" height="636" loop="" muted="" playsinline="" width="1082"></video> <div class="a__caption astro-meqjtcea"> <div class="a__caption__text astro-meqjtcea"> Honor Society Films </div><!-- .a__caption__text --> <div class="a__caption__key astro-meqjtcea">
#wr89-0015/33 </div><!-- .a__caption__key --> </div><!-- .a__caption --> </a> </div><!-- .a__inner --> </a-work> <a-work class="s__scene__work s__scene__work--video js-work astro-meqjtcea" progress="1" style="--size: 0.5604178226986669; --y: 0.6011641451227234; --progress: 1;"> <div class="a__inner astro-meqjtcea"> <a class="astro-meqjtcea" href="https://duten.com" target="_blank"> <video class="a__video js-video astro-meqjtcea" data-src="/_astro/Duten-Products.D0b17Eb7.mp4" height="636" loop="" muted="" playsinline="" width="1082"></video> <div class="a__caption astro-meqjtcea"> <div class="a__caption__text astro-meqjtcea"> Duten </div><!-- .a__caption__text --> <div class="a__caption__key astro-meqjtcea">
#sk2y-0016/33 </div><!-- .a__caption__key --> </div><!-- .a__caption --> </a> </div><!-- .a__inner --> </a-work> <a-work class="s__scene__work s__scene__work--video js-work astro-meqjtcea" progress="1" style="--size: 0.8852254692688447; --y: -0.7636947301116701; --progress: 1;"> <div class="a__inner astro-meqjtcea"> <a class="astro-meqjtcea" href="https://codepen.io/wodniack" target="_blank"> <video class="a__video js-video astro-meqjtcea" data-src="/_astro/Pen-6.CMyHVvwm.mp4" height="636" loop="" muted="" playsinline="" width="1082"></video> <div class="a__caption astro-meqjtcea"> <div class="a__caption__text astro-meqjtcea"> CodePen </div><!-- .a__caption__text --> <div class="a__caption__key astro-meqjtcea">
#hdur-0017/33 </div><!-- .a__caption__key --> </div><!-- .a__caption --> </a> </div><!-- .a__inner --> </a-work> <a-work class="s__scene__work s__scene__work--video js-work astro-meqjtcea" progress="1" style="--size: 0.6387882244760945; --y: 0.9426372728016926; --progress: 1;"> <div class="a__inner astro-meqjtcea"> <a class="astro-meqjtcea" href="https://incredibles.dev" target="_blank"> <video class="a__video js-video astro-meqjtcea" data-src="/_astro/Incredibles-Ray.lxZWWm77.mp4" height="636" loop="" muted="" playsinline="" width="1082"></video> <div class="a__caption astro-meqjtcea"> <div class="a__caption__text astro-meqjtcea"> incredibles </div><!-- .a__caption__text --> <div class="a__caption__key astro-meqjtcea">
#ew9c-0018/33 </div><!-- .a__caption__key --> </div><!-- .a__caption --> </a> </div><!-- .a__inner --> </a-work> <a-work class="s__scene__work s__scene__work--video js-work astro-meqjtcea" progress="1" style="--size: 0.5489756308353358; --y: -0.5141618840737404; --progress: 1;"> <div class="a__inner astro-meqjtcea"> <a class="astro-meqjtcea" href="https://www.rudlundschwarm.at/" target="_blank"> <video class="a__video js-video astro-meqjtcea" data-src="/_astro/Rudl-Scroll.DmL08CDl.mp4" height="636" loop="" muted="" playsinline="" width="1082"></video> <div class="a__caption astro-meqjtcea"> <div class="a__caption__text astro-meqjtcea"> Rudl und Schwarm </div><!-- .a__caption__text --> <div class="a__caption__key astro-meqjtcea">
#df7f-0019/33 </div><!-- .a__caption__key --> </div><!-- .a__caption --> </a> </div><!-- .a__inner --> </a-work> <a-work class="s__scene__work s__scene__work--video js-work astro-meqjtcea" progress="1" style="--size: 0.5857474928966289; --y: 0.6849982408158481; --progress: 1;"> <div class="a__inner astro-meqjtcea"> <a class="astro-meqjtcea" href="https://generousbranding.com" target="_blank"> <video class="a__video js-video astro-meqjtcea" data-src="/_astro/Generous-Scroll1.0UenKQ_7.mp4" height="636" loop="" muted="" playsinline="" width="1082"></video> <div class="a__caption astro-meqjtcea"> <div class="a__caption__text astro-meqjtcea"> Generous Branding </div><!-- .a__caption__text --> <div class="a__caption__key astro-meqjtcea">
#ysjj-0020/33 </div><!-- .a__caption__key --> </div><!-- .a__caption --> </a> </div><!-- .a__inner --> </a-work> <a-work class="s__scene__work s__scene__work--video js-work astro-meqjtcea" progress="1" style="--size: 0.8024530968433151; --y: -0.8303017275157882; --progress: 1;"> <div class="a__inner astro-meqjtcea"> <a class="astro-meqjtcea" href="#" target="_blank"> <video class="a__video js-video astro-meqjtcea" data-src="/_astro/Caxis-Transition.BHJmdqmC.mp4" height="636" loop="" muted="" playsinline="" width="1082"></video> <div class="a__caption astro-meqjtcea"> <div class="a__caption__text astro-meqjtcea"> Caxis </div><!-- .a__caption__text --> <div class="a__caption__key astro-meqjtcea">
#k81d-0021/33 </div><!-- .a__caption__key --> </div><!-- .a__caption --> </a> </div><!-- .a__inner --> </a-work> <a-work class="s__scene__work s__scene__work--video js-work astro-meqjtcea" progress="1" style="--size: 0.5751257589295906; --y: 0.6193307457606357; --progress: 1;"> <div class="a__inner astro-meqjtcea"> <a class="astro-meqjtcea" href="https://nodcoding.com" target="_blank"> <video class="a__video js-video astro-meqjtcea" data-src="/_astro/Nod-Links.De8GBDpL.mp4" height="636" loop="" muted="" playsinline="" width="1082"></video> <div class="a__caption astro-meqjtcea"> <div class="a__caption__text astro-meqjtcea"> Nod Coding Bootcamp </div><!-- .a__caption__text --> <div class="a__caption__key astro-meqjtcea">
#xlxx-0022/33 </div><!-- .a__caption__key --> </div><!-- .a__caption --> </a> </div><!-- .a__inner --> </a-work> <a-work class="s__scene__work s__scene__work--video js-work astro-meqjtcea" progress="1" style="--size: 0.6441798402810985; --y: -0.7331411250846986; --progress: 1;"> <div class="a__inner astro-meqjtcea"> <a class="astro-meqjtcea" href="https://codepen.io/wodniack" target="_blank"> <video class="a__video js-video astro-meqjtcea" data-src="/_astro/Pen-7.BzyGmIZt.mp4" height="636" loop="" muted="" playsinline="" width="1082"></video> <div class="a__caption astro-meqjtcea"> <div class="a__caption__text astro-meqjtcea"> CodePen </div><!-- .a__caption__text --> <div class="a__caption__key astro-meqjtcea">
#i31t-0023/33 </div><!-- .a__caption__key --> </div><!-- .a__caption --> </a> </div><!-- .a__inner --> </a-work> <a-work class="s__scene__work s__scene__work--video js-work astro-meqjtcea" progress="1" style="--size: 0.6756175997189942; --y: 0.9332995385463589; --progress: 1;"> <div class="a__inner astro-meqjtcea"> <a class="astro-meqjtcea" href="https://generousbranding.com" target="_blank"> <video class="a__video js-video astro-meqjtcea" data-src="/_astro/Generous-Slider.SVzdHB83.mp4" height="636" loop="" muted="" playsinline="" width="1082"></video> <div class="a__caption astro-meqjtcea"> <div class="a__caption__text astro-meqjtcea"> Generous Branding </div><!-- .a__caption__text --> <div class="a__caption__key astro-meqjtcea">
#h37u-0024/33 </div><!-- .a__caption__key --> </div><!-- .a__caption --> </a> </div><!-- .a__inner --> </a-work> <a-work class="s__scene__work s__scene__work--video js-work astro-meqjtcea" progress="1" style="--size: 0.760031521569072; --y: -0.7979060859622543; --progress: 1;"> <div class="a__inner astro-meqjtcea"> <a class="astro-meqjtcea" href="https://duten.com" target="_blank"> <video class="a__video js-video astro-meqjtcea" data-src="/_astro/Duten-Shapes.DvaTdo7l.mp4" height="636" loop="" muted="" playsinline="" width="1082"></video> <div class="a__caption astro-meqjtcea"> <div class="a__caption__text astro-meqjtcea"> Duten </div><!-- .a__caption__text --> <div class="a__caption__key astro-meqjtcea">
#y2h3-0025/33 </div><!-- .a__caption__key --> </div><!-- .a__caption --> </a> </div><!-- .a__inner --> </a-work> <a-work class="s__scene__work s__scene__work--video js-work astro-meqjtcea" progress="1" style="--size: 0.8144249760896332; --y: 0.6794569128202858; --progress: 1;"> <div class="a__inner astro-meqjtcea"> <a class="astro-meqjtcea" href="https://nodcoding.com" target="_blank"> <video class="a__video js-video astro-meqjtcea" data-src="/_astro/Nod-Flower.VBa6NQlZ.mp4" height="636" loop="" muted="" playsinline="" width="1082"></video> <div class="a__caption astro-meqjtcea"> <div class="a__caption__text astro-meqjtcea"> Nod Coding Bootcamp </div><!-- .a__caption__text --> <div class="a__caption__key astro-meqjtcea">
#8hgj-0026/33 </div><!-- .a__caption__key --> </div><!-- .a__caption --> </a> </div><!-- .a__inner --> </a-work> <a-work class="s__scene__work s__scene__work--video js-work astro-meqjtcea" progress="1" style="--size: 0.5762180006386175; --y: -0.6094855230563196; --progress: 1;"> <div class="a__inner astro-meqjtcea"> <a class="astro-meqjtcea" href="https://qbitcapital.xyz" target="_blank"> <video class="a__video js-video astro-meqjtcea" data-src="/_astro/Qbit-Shape.BySbOvpY.mp4" height="636" loop="" muted="" playsinline="" width="1082"></video> <div class="a__caption astro-meqjtcea"> <div class="a__caption__text astro-meqjtcea"> QBIT Capital </div><!-- .a__caption__text --> <div class="a__caption__key astro-meqjtcea">
#rce6-0027/33 </div><!-- .a__caption__key --> </div><!-- .a__caption --> </a> </div><!-- .a__inner --> </a-work> <a-work class="s__scene__work s__scene__work--video js-work astro-meqjtcea" progress="1" style="--size: 0.987683588342241; --y: 0.6562783461377055; --progress: 1;"> <div class="a__inner astro-meqjtcea"> <a class="astro-meqjtcea" href="https://nodcoding.com" target="_blank"> <video class="a__video js-video astro-meqjtcea" data-src="/_astro/Nod-Intro.DujLdLjK.mp4" height="636" loop="" muted="" playsinline="" width="1082"></video> <div class="a__caption astro-meqjtcea"> <div class="a__caption__text astro-meqjtcea"> Nod Coding Bootcamp </div><!-- .a__caption__text --> <div class="a__caption__key astro-meqjtcea">
#j1eu-0028/33 </div><!-- .a__caption__key --> </div><!-- .a__caption --> </a> </div><!-- .a__inner --> </a-work> <a-work class="s__scene__work s__scene__work--video js-work astro-meqjtcea" progress="1" style="--size: 0.6713478560638388; --y: -0.8008605608949544; --progress: 1;"> <div class="a__inner astro-meqjtcea"> <a class="astro-meqjtcea" href="https://codepen.io/wodniack" target="_blank"> <video class="a__video js-video astro-meqjtcea" data-src="/_astro/Pen-8.G-raKJSF.mp4" height="636" loop="" muted="" playsinline="" width="1082"></video> <div class="a__caption astro-meqjtcea"> <div class="a__caption__text astro-meqjtcea"> CodePen </div><!-- .a__caption__text --> <div class="a__caption__key astro-meqjtcea">
#fjxf-0029/33 </div><!-- .a__caption__key --> </div><!-- .a__caption --> </a> </div><!-- .a__inner --> </a-work> <a-work class="s__scene__work s__scene__work--video js-work astro-meqjtcea" progress="1" style="--size: 0.646551044359829; --y: 0.715516134946092; --progress: 1;"> <div class="a__inner astro-meqjtcea"> <a class="astro-meqjtcea" href="#" target="_blank"> <video class="a__video js-video astro-meqjtcea" data-src="/_astro/Kohost-Intro.CA4nMYt7.mp4" height="636" loop="" muted="" playsinline="" width="1082"></video> <div class="a__caption astro-meqjtcea"> <div class="a__caption__text astro-meqjtcea"> Kohost </div><!-- .a__caption__text --> <div class="a__caption__key astro-meqjtcea">
#oak5-0030/33 </div><!-- .a__caption__key --> </div><!-- .a__caption --> </a> </div><!-- .a__inner --> </a-work> <a-work class="s__scene__work s__scene__work--video js-work astro-meqjtcea" progress="1" style="--size: 0.5777125960680562; --y: -0.7075598919370734; --progress: 1;"> <div class="a__inner astro-meqjtcea"> <a class="astro-meqjtcea" href="https://www.saniswiss.com/" target="_blank"> <video class="a__video js-video astro-meqjtcea" data-src="/_astro/Saniswiss-Products.D7GK9AEH.mp4" height="636" loop="" muted="" playsinline="" width="1082"></video> <div class="a__caption astro-meqjtcea"> <div class="a__caption__text astro-meqjtcea"> Saniswiss </div><!-- .a__caption__text --> <div class="a__caption__key astro-meqjtcea">
#ad2n-0031/33 </div><!-- .a__caption__key --> </div><!-- .a__caption --> </a> </div><!-- .a__inner --> </a-work> <a-work class="s__scene__work s__scene__work--video js-work astro-meqjtcea" progress="1" style="--size: 0.9734464138555832; --y: 0.8729425163044388; --progress: 1;"> <div class="a__inner astro-meqjtcea"> <a class="astro-meqjtcea" href="#" target="_blank"> <video class="a__video js-video astro-meqjtcea" data-src="/_astro/Hapu-Interaction.CqeTD_4p.mp4" height="636" loop="" muted="" playsinline="" width="1082"></video> <div class="a__caption astro-meqjtcea"> <div class="a__caption__text astro-meqjtcea"> Hapu </div><!-- .a__caption__text --> <div class="a__caption__key astro-meqjtcea">
#4p7r-0032/33 </div><!-- .a__caption__key --> </div><!-- .a__caption --> </a> </div><!-- .a__inner --> </a-work> <span class="s__scene__letter js-letter" data-letter="W" style="top: 72px; left: 587.635px; z-index: 1; --ix: -5.5; --iy: -0.6; --ap: 1; --p: -1;">W</span><span class="s__scene__letter js-letter" data-letter="W" style="top: 72px; left: 587.635px; z-index: 3; --ix: -4.5; --iy: -0.6; --ap: 0.8181818181818181; --p: -0.8181818181818181;">W</span><span class="s__scene__letter js-letter" data-letter="W" style="top: 72px; left: 587.635px; z-index: 1; --ix: -3.5; --iy: -0.6; --ap: 0.6363636363636364; --p: -0.6363636363636364;">W</span><span class="s__scene__letter js-letter" data-letter="W" style="top: 72px; left: 587.635px; z-index: 1; --ix: -2.5; --iy: -0.6; --ap: 0.4545454545454546; --p: -0.4545454545454546;">W</span><span class="s__scene__letter js-letter" data-letter="W" style="top: 72px; left: 587.635px; z-index: 1; --ix: -1.5; --iy: -0.6; --ap: 0.2727272727272727; --p: -0.2727272727272727;">W</span><span class="s__scene__letter js-letter" data-letter="W" style="top: 72px; left: 587.635px; z-index: 1; --ix: -0.5; --iy: -0.6; --ap: 0.09090909090909094; --p: -0.09090909090909094;">W</span><span class="s__scene__letter js-letter" data-letter="W" style="top: 72px; left: 587.635px; z-index: 3; --ix: 0.5; --iy: -0.6; --ap: 0.09090909090909083; --p: 0.09090909090909083;">W</span><span class="s__scene__letter js-letter" data-letter="W" style="top: 72px; left: 587.635px; z-index: 1; --ix: 1.5; --iy: -0.6; --ap: 0.2727272727272727; --p: 0.2727272727272727;">W</span><span class="s__scene__letter js-letter" data-letter="W" style="top: 72px; left: 587.635px; z-index: 1; --ix: 2.5; --iy: -0.6; --ap: 0.4545454545454546; --p: 0.4545454545454546;">W</span><span class="s__scene__letter js-letter" data-letter="W" style="top: 72px; left: 587.635px; z-index: 1; --ix: 3.5; --iy: -0.6; --ap: 0.6363636363636365; --p: 0.6363636363636365;">W</span><span class="s__scene__letter js-letter" data-letter="W" style="top: 72px; left: 587.635px; z-index: 1; --ix: 4.5; --iy: -0.6; --ap: 0.8181818181818181; --p: 0.8181818181818181;">W</span><span class="s__scene__letter js-letter" data-letter="O" style="top: 225px; left: 607.104px; z-index: 1; --ix: -8.5; --iy: -0.19999999999999996; --ap: 1; --p: -1;">O</span><span class="s__scene__letter js-letter" data-letter="O" style="top: 225px; left: 607.104px; z-index: 1; --ix: -7.5; --iy: -0.19999999999999996; --ap: 0.8823529411764706; --p: -0.8823529411764706;">O</span><span class="s__scene__letter js-letter" data-letter="O" style="top: 225px; left: 607.104px; z-index: 1; --ix: -6.5; --iy: -0.19999999999999996; --ap: 0.7647058823529411; --p: -0.7647058823529411;">O</span><span class="s__scene__letter js-letter" data-letter="O" style="top: 225px; left: 607.104px; z-index: 1; --ix: -5.5; --iy: -0.19999999999999996; --ap: 0.6470588235294117; --p: -0.6470588235294117;">O</span><span class="s__scene__letter js-letter" data-letter="O" style="top: 225px; left: 607.104px; z-index: 1; --ix: -4.5; --iy: -0.19999999999999996; --ap: 0.5294117647058824; --p: -0.5294117647058824;">O</span><span class="s__scene__letter js-letter" data-letter="O" style="top: 225px; left: 607.104px; z-index: 1; --ix: -3.5; --iy: -0.19999999999999996; --ap: 0.4117647058823529; --p: -0.4117647058823529;">O</span><span class="s__scene__letter js-letter" data-letter="O" style="top: 225px; left: 607.104px; z-index: 1; --ix: -2.5; --iy: -0.19999999999999996; --ap: 0.2941176470588235; --p: -0.2941176470588235;">O</span><span class="s__scene__letter js-letter" data-letter="O" style="top: 225px; left: 607.104px; z-index: 1; --ix: -1.5; --iy: -0.19999999999999996; --ap: 0.17647058823529416; --p: -0.17647058823529416;">O</span><span class="s__scene__letter js-letter" data-letter="O" style="top: 225px; left: 607.104px; z-index: 1; --ix: -0.5; --iy: -0.19999999999999996; --ap: 0.05882352941176472; --p: -0.05882352941176472;">O</span><span class="s__scene__letter js-letter" data-letter="O" style="top: 225px; left: 607.104px; z-index: 1; --ix: 0.5; --iy: -0.19999999999999996; --ap: 0.05882352941176472; --p: 0.05882352941176472;">O</span><span class="s__scene__letter js-letter" data-letter="O" style="top: 225px; left: 607.104px; z-index: 1; --ix: 1.5; --iy: -0.19999999999999996; --ap: 0.17647058823529416; --p: 0.17647058823529416;">O</span><span class="s__scene__letter js-letter" data-letter="O" style="top: 225px; left: 607.104px; z-index: 1; --ix: 2.5; --iy: -0.19999999999999996; --ap: 0.2941176470588236; --p: 0.2941176470588236;">O</span><span class="s__scene__letter js-letter" data-letter="O" style="top: 225px; left: 607.104px; z-index: 1; --ix: 3.5; --iy: -0.19999999999999996; --ap: 0.41176470588235303; --p: 0.41176470588235303;">O</span><span class="s__scene__letter js-letter" data-letter="O" style="top: 225px; left: 607.104px; z-index: 1; --ix: 4.5; --iy: -0.19999999999999996; --ap: 0.5294117647058822; --p: 0.5294117647058822;">O</span><span class="s__scene__letter js-letter" data-letter="O" style="top: 225px; left: 607.104px; z-index: 1; --ix: 5.5; --iy: -0.19999999999999996; --ap: 0.6470588235294117; --p: 0.6470588235294117;">O</span><span class="s__scene__letter js-letter" data-letter="O" style="top: 225px; left: 607.104px; z-index: 1; --ix: 6.5; --iy: -0.19999999999999996; --ap: 0.7647058823529411; --p: 0.7647058823529411;">O</span><span class="s__scene__letter js-letter" data-letter="O" style="top: 225px; left: 607.104px; z-index: 1; --ix: 7.5; --iy: -0.19999999999999996; --ap: 0.8823529411764706; --p: 0.8823529411764706;">O</span><span class="s__scene__letter js-letter" data-letter="R" style="top: 378px; left: 607.104px; z-index: 1; --ix: -8.5; --iy: 0.19999999999999996; --ap: 1; --p: -1;">R</span><span class="s__scene__letter js-letter" data-letter="R" style="top: 378px; left: 607.104px; z-index: 1; --ix: -7.5; --iy: 0.19999999999999996; --ap: 0.8823529411764706; --p: -0.8823529411764706;">R</span><span class="s__scene__letter js-letter" data-letter="R" style="top: 378px; left: 607.104px; z-index: 1; --ix: -6.5; --iy: 0.19999999999999996; --ap: 0.7647058823529411; --p: -0.7647058823529411;">R</span><span class="s__scene__letter js-letter" data-letter="R" style="top: 378px; left: 607.104px; z-index: 1; --ix: -5.5; --iy: 0.19999999999999996; --ap: 0.6470588235294117; --p: -0.6470588235294117;">R</span><span class="s__scene__letter js-letter" data-letter="R" style="top: 378px; left: 607.104px; z-index: 1; --ix: -4.5; --iy: 0.19999999999999996; --ap: 0.5294117647058824; --p: -0.5294117647058824;">R</span><span class="s__scene__letter js-letter" data-letter="R" style="top: 378px; left: 607.104px; z-index: 1; --ix: -3.5; --iy: 0.19999999999999996; --ap: 0.4117647058823529; --p: -0.4117647058823529;">R</span><span class="s__scene__letter js-letter" data-letter="R" style="top: 378px; left: 607.104px; z-index: 1; --ix: -2.5; --iy: 0.19999999999999996; --ap: 0.2941176470588235; --p: -0.2941176470588235;">R</span><span class="s__scene__letter js-letter" data-letter="R" style="top: 378px; left: 607.104px; z-index: 1; --ix: -1.5; --iy: 0.19999999999999996; --ap: 0.17647058823529416; --p: -0.17647058823529416;">R</span><span class="s__scene__letter js-letter" data-letter="R" style="top: 378px; left: 607.104px; z-index: 1; --ix: -0.5; --iy: 0.19999999999999996; --ap: 0.05882352941176472; --p: -0.05882352941176472;">R</span><span class="s__scene__letter js-letter" data-letter="R" style="top: 378px; left: 607.104px; z-index: 1; --ix: 0.5; --iy: 0.19999999999999996; --ap: 0.05882352941176472; --p: 0.05882352941176472;">R</span><span class="s__scene__letter js-letter" data-letter="R" style="top: 378px; left: 607.104px; z-index: 1; --ix: 1.5; --iy: 0.19999999999999996; --ap: 0.17647058823529416; --p: 0.17647058823529416;">R</span><span class="s__scene__letter js-letter" data-letter="R" style="top: 378px; left: 607.104px; z-index: 1; --ix: 2.5; --iy: 0.19999999999999996; --ap: 0.2941176470588236; --p: 0.2941176470588236;">R</span><span class="s__scene__letter js-letter" data-letter="R" style="top: 378px; left: 607.104px; z-index: 1; --ix: 3.5; --iy: 0.19999999999999996; --ap: 0.41176470588235303; --p: 0.41176470588235303;">R</span><span class="s__scene__letter js-letter" data-letter="R" style="top: 378px; left: 607.104px; z-index: 1; --ix: 4.5; --iy: 0.19999999999999996; --ap: 0.5294117647058822; --p: 0.5294117647058822;">R</span><span class="s__scene__letter js-letter" data-letter="R" style="top: 378px; left: 607.104px; z-index: 1; --ix: 5.5; --iy: 0.19999999999999996; --ap: 0.6470588235294117; --p: 0.6470588235294117;">R</span><span class="s__scene__letter js-letter" data-letter="R" style="top: 378px; left: 607.104px; z-index: 1; --ix: 6.5; --iy: 0.19999999999999996; --ap: 0.7647058823529411; --p: 0.7647058823529411;">R</span><span class="s__scene__letter js-letter" data-letter="R" style="top: 378px; left: 607.104px; z-index: 1; --ix: 7.5; --iy: 0.19999999999999996; --ap: 0.8823529411764706; --p: 0.8823529411764706;">R</span><span class="s__scene__letter js-letter" data-letter="K" style="top: 531px; left: 604.208px; z-index: 1; --ix: -7.5; --iy: 0.6000000000000001; --ap: 1; --p: -1;">K</span><span class="s__scene__letter js-letter" data-letter="K" style="top: 531px; left: 604.208px; z-index: 1; --ix: -6.5; --iy: 0.6000000000000001; --ap: 0.8666666666666667; --p: -0.8666666666666667;">K</span><span class="s__scene__letter js-letter" data-letter="K" style="top: 531px; left: 604.208px; z-index: 1; --ix: -5.5; --iy: 0.6000000000000001; --ap: 0.7333333333333334; --p: -0.7333333333333334;">K</span><span class="s__scene__letter js-letter" data-letter="K" style="top: 531px; left: 604.208px; z-index: 3; --ix: -4.5; --iy: 0.6000000000000001; --ap: 0.6; --p: -0.6;">K</span><span class="s__scene__letter js-letter" data-letter="K" style="top: 531px; left: 604.208px; z-index: 1; --ix: -3.5; --iy: 0.6000000000000001; --ap: 0.4666666666666667; --p: -0.4666666666666667;">K</span><span class="s__scene__letter js-letter" data-letter="K" style="top: 531px; left: 604.208px; z-index: 1; --ix: -2.5; --iy: 0.6000000000000001; --ap: 0.33333333333333337; --p: -0.33333333333333337;">K</span><span class="s__scene__letter js-letter" data-letter="K" style="top: 531px; left: 604.208px; z-index: 1; --ix: -1.5; --iy: 0.6000000000000001; --ap: 0.19999999999999996; --p: -0.19999999999999996;">K</span><span class="s__scene__letter js-letter" data-letter="K" style="top: 531px; left: 604.208px; z-index: 1; --ix: -0.5; --iy: 0.6000000000000001; --ap: 0.06666666666666665; --p: -0.06666666666666665;">K</span><span class="s__scene__letter js-letter" data-letter="K" style="top: 531px; left: 604.208px; z-index: 3; --ix: 0.5; --iy: 0.6000000000000001; --ap: 0.06666666666666665; --p: 0.06666666666666665;">K</span><span class="s__scene__letter js-letter" data-letter="K" style="top: 531px; left: 604.208px; z-index: 1; --ix: 1.5; --iy: 0.6000000000000001; --ap: 0.19999999999999996; --p: 0.19999999999999996;">K</span><span class="s__scene__letter js-letter" data-letter="K" style="top: 531px; left: 604.208px; z-index: 1; --ix: 2.5; --iy: 0.6000000000000001; --ap: 0.33333333333333326; --p: 0.33333333333333326;">K</span><span class="s__scene__letter js-letter" data-letter="K" style="top: 531px; left: 604.208px; z-index: 1; --ix: 3.5; --iy: 0.6000000000000001; --ap: 0.46666666666666656; --p: 0.46666666666666656;">K</span><span class="s__scene__letter js-letter" data-letter="K" style="top: 531px; left: 604.208px; z-index: 1; --ix: 4.5; --iy: 0.6000000000000001; --ap: 0.6000000000000001; --p: 0.6000000000000001;">K</span><span class="s__scene__letter js-letter" data-letter="K" style="top: 531px; left: 604.208px; z-index: 3; --ix: 5.5; --iy: 0.6000000000000001; --ap: 0.7333333333333334; --p: 0.7333333333333334;">K</span><span class="s__scene__letter js-letter" data-letter="K" style="top: 531px; left: 604.208px; z-index: 1; --ix: 6.5; --iy: 0.6000000000000001; --ap: 0.8666666666666667; --p: 0.8666666666666667;">K</span></div><!-- .s__scene --> <canvas class="s__canvas js-canvas astro-doefkd43" height="720" width="1280"></canvas> </div><!-- .s__inner --> <div class="s__mask-outer astro-doefkd43"> <div class="s__mask js-mask astro-doefkd43" style="translate: none; rotate: none; scale: none; transform: translate(0px, 0px);"> <svg class="s__mask__svg js-mask-svg astro-doefkd43" style="width: 1248px; height: 720px;"> <path class="s__mask__path-inner js-mask-path-inner astro-doefkd43" d="M -1 0 L 1250 0 L 1250 720 L -1 720 Z M 535.9896240234375 176 A 88 88 0 0 1 711.9896240234375 176 L 711.9896240234375 544 A 88 88 0 0 1 535.9896240234375 544 Z"></path> <path class="s__mask__path-outer js-mask-path-outer astro-doefkd43" d="M -1 0 L 1250 0 L 1250 720 L -1 720 Z M 519.9896240234375 176 A 104 104 0 0 1 727.9896240234375 176 L 727.9896240234375 544 A 104 104 0 0 1 519.9896240234375 544 Z"></path> <path class="s__mask__path-lines js-mask-path-lines astro-doefkd43" d="M 104 0 L 104 720 M 208 0 L 208 720 M 312 0 L 312 720 M 416 0 L 416 720 M 520 0 L 520 720 M 624 0 L 624 720 M 728 0 L 728 720 M 832 0 L 832 720 M 936 0 L 936 720 M 1040 0 L 1040 720 M 1144 0 L 1144 720 M 0 0 L 1248 0 M 0 72 L 1248 72 M 0 144 L 1248 144 M 0 216 L 1248 216 M 0 288 L 1248 288 M 0 360 L 1248 360 M 0 432 L 1248 432 M 0 504 L 1248 504 M 0 576 L 1248 576 M 0 648 L 1248 648 " style='clip-path: path(evenodd, "M -1 0 L 1250 0 L 1250 720 L -1 720 Z M 519.99 176 A 104 104 0 0 1 727.99 176 L 727.99 544 A 104 104 0 0 1 519.99 544 Z");'></path> </svg> </div><!-- .s__mask --> </div><!-- .s__mask-outer --> <div class="s__ruler js-ruler astro-doefkd43"></div> </div><!-- .s__outer --> </section> <a-separator class="style--secondary astro-j7pv25f6 astro-rdpcjfuz is-out-of-view is-out-of-view-bottom" data-intersect=""> <span class="a__triangle astro-rdpcjfuz"></span> <span class="a__binaries astro-rdpcjfuz"> <span class="a__code js-code astro-rdpcjfuz"> <span class="a__char a__char--1 js-char astro-rdpcjfuz"> 1 </span><span class="a__char a__char--0 js-char astro-rdpcjfuz"> 0 </span><span class="a__char a__char--0 js-char astro-rdpcjfuz"> 0 </span><span class="a__char a__char--0 js-char astro-rdpcjfuz"> 0 </span><span class="a__char a__char--1 js-char astro-rdpcjfuz"> 1 </span><span class="a__char a__char--0 js-char astro-rdpcjfuz"> 0 </span><span class="a__char a__char--0 js-char astro-rdpcjfuz"> 0 </span><span class="a__char a__char--blank js-char astro-rdpcjfuz"> </span><span class="a__char a__char--1 js-char astro-rdpcjfuz"> 1 </span><span class="a__char a__char--1 js-char astro-rdpcjfuz"> 1 </span><span class="a__char a__char--0 js-char astro-rdpcjfuz"> 0 </span><span class="a__char a__char--1 js-char astro-rdpcjfuz"> 1 </span><span class="a__char a__char--1 js-char astro-rdpcjfuz"> 1 </span><span class="a__char a__char--1 js-char astro-rdpcjfuz"> 1 </span><span class="a__char a__char--1 js-char astro-rdpcjfuz"> 1 </span> </span> <span class="a__stripes astro-rdpcjfuz"></span> <span class="a__code js-code astro-rdpcjfuz"> <span class="a__char a__char--1 js-char astro-rdpcjfuz"> 1 </span><span class="a__char a__char--0 js-char astro-rdpcjfuz"> 0 </span><span class="a__char a__char--1 js-char astro-rdpcjfuz"> 1 </span><span class="a__char a__char--0 js-char astro-rdpcjfuz"> 0 </span><span class="a__char a__char--1 js-char astro-rdpcjfuz"> 1 </span><span class="a__char a__char--0 js-char astro-rdpcjfuz"> 0 </span><span class="a__char a__char--0 js-char astro-rdpcjfuz"> 0 </span><span class="a__char a__char--blank js-char astro-rdpcjfuz"> </span><span class="a__char a__char--1 js-char astro-rdpcjfuz"> 1 </span><span class="a__char a__char--1 js-char astro-rdpcjfuz"> 1 </span><span class="a__char a__char--0 js-char astro-rdpcjfuz"> 0 </span><span class="a__char a__char--1 js-char astro-rdpcjfuz"> 1 </span><span class="a__char a__char--0 js-char astro-rdpcjfuz"> 0 </span><span class="a__char a__char--0 js-char astro-rdpcjfuz"> 0 </span><span class="a__char a__char--0 js-char astro-rdpcjfuz"> 0 </span><span class="a__char a__char--blank js-char astro-rdpcjfuz"> </span><span class="a__char a__char--1 js-char astro-rdpcjfuz"> 1 </span><span class="a__char a__char--1 js-char astro-rdpcjfuz"> 1 </span><span class="a__char a__char--0 js-char astro-rdpcjfuz"> 0 </span><span class="a__char a__char--1 js-char astro-rdpcjfuz"> 1 </span><span class="a__char a__char--0 js-char astro-rdpcjfuz"> 0 </span><span class="a__char a__char--0 js-char astro-rdpcjfuz"> 0 </span><span class="a__char a__char--1 js-char astro-rdpcjfuz"> 1 </span><span class="a__char a__char--blank js-char astro-rdpcjfuz"> </span><span class="a__char a__char--1 js-char astro-rdpcjfuz"> 1 </span><span class="a__char a__char--1 js-char astro-rdpcjfuz"> 1 </span><span class="a__char a__char--0 js-char astro-rdpcjfuz"> 0 </span><span class="a__char a__char--1 js-char astro-rdpcjfuz"> 1 </span><span class="a__char a__char--1 js-char astro-rdpcjfuz"> 1 </span><span class="a__char a__char--1 js-char astro-rdpcjfuz"> 1 </span><span class="a__char a__char--0 js-char astro-rdpcjfuz"> 0 </span><span class="a__char a__char--blank js-char astro-rdpcjfuz"> </span><span class="a__char a__char--1 js-char astro-rdpcjfuz"> 1 </span><span class="a__char a__char--1 js-char astro-rdpcjfuz"> 1 </span><span class="a__char a__char--0 js-char astro-rdpcjfuz"> 0 </span><span class="a__char a__char--0 js-char astro-rdpcjfuz"> 0 </span><span class="a__char a__char--1 js-char astro-rdpcjfuz"> 1 </span><span class="a__char a__char--1 js-char astro-rdpcjfuz"> 1 </span><span class="a__char a__char--1 js-char astro-rdpcjfuz"> 1 </span><span class="a__char a__char--blank js-char astro-rdpcjfuz"> </span><span class="a__char a__char--1 js-char astro-rdpcjfuz"> 1 </span><span class="a__char a__char--1 js-char astro-rdpcjfuz"> 1 </span><span class="a__char a__char--1 js-char astro-rdpcjfuz"> 1 </span><span class="a__char a__char--0 js-char astro-rdpcjfuz"> 0 </span><span class="a__char a__char--0 js-char astro-rdpcjfuz"> 0 </span><span class="a__char a__char--1 js-char astro-rdpcjfuz"> 1 </span><span class="a__char a__char--1 js-char astro-rdpcjfuz"> 1 </span> </span> <span class="a__stripes astro-rdpcjfuz"></span> <span class="a__code js-code astro-rdpcjfuz"> <span class="a__char a__char--1 js-char astro-rdpcjfuz"> 1 </span><span class="a__char a__char--0 js-char astro-rdpcjfuz"> 0 </span><span class="a__char a__char--1 js-char astro-rdpcjfuz"> 1 </span><span class="a__char a__char--1 js-char astro-rdpcjfuz"> 1 </span><span class="a__char a__char--0 js-char astro-rdpcjfuz"> 0 </span><span class="a__char a__char--0 js-char astro-rdpcjfuz"> 0 </span><span class="a__char a__char--1 js-char astro-rdpcjfuz"> 1 </span><span class="a__char a__char--blank js-char astro-rdpcjfuz"> </span><span class="a__char a__char--1 js-char astro-rdpcjfuz"> 1 </span><span class="a__char a__char--1 js-char astro-rdpcjfuz"> 1 </span><span class="a__char a__char--0 js-char astro-rdpcjfuz"> 0 </span><span class="a__char a__char--1 js-char astro-rdpcjfuz"> 1 </span><span class="a__char a__char--1 js-char astro-rdpcjfuz"> 1 </span><span class="a__char a__char--1 js-char astro-rdpcjfuz"> 1 </span><span class="a__char a__char--1 js-char astro-rdpcjfuz"> 1 </span><span class="a__char a__char--blank js-char astro-rdpcjfuz"> </span><span class="a__char a__char--1 js-char astro-rdpcjfuz"> 1 </span><span class="a__char a__char--1 js-char astro-rdpcjfuz"> 1 </span><span class="a__char a__char--1 js-char astro-rdpcjfuz"> 1 </span><span class="a__char a__char--0 js-char astro-rdpcjfuz"> 0 </span><span class="a__char a__char--1 js-char astro-rdpcjfuz"> 1 </span><span class="a__char a__char--0 js-char astro-rdpcjfuz"> 0 </span><span class="a__char a__char--1 js-char astro-rdpcjfuz"> 1 </span><span class="a__char a__char--blank js-char astro-rdpcjfuz"> </span><span class="a__char a__char--1 js-char astro-rdpcjfuz"> 1 </span><span class="a__char a__char--1 js-char astro-rdpcjfuz"> 1 </span><span class="a__char a__char--1 js-char astro-rdpcjfuz"> 1 </span><span class="a__char a__char--0 js-char astro-rdpcjfuz"> 0 </span><span class="a__char a__char--0 js-char astro-rdpcjfuz"> 0 </span><span class="a__char a__char--1 js-char astro-rdpcjfuz"> 1 </span><span class="a__char a__char--0 js-char astro-rdpcjfuz"> 0 </span> </span> <span class="a__stripes astro-rdpcjfuz"></span> <span class="a__code js-code astro-rdpcjfuz"> <span class="a__char a__char--1 js-char astro-rdpcjfuz"> 1 </span><span class="a__char a__char--0 js-char astro-rdpcjfuz"> 0 </span><span class="a__char a__char--1 js-char astro-rdpcjfuz"> 1 </span><span class="a__char a__char--0 js-char astro-rdpcjfuz"> 0 </span><span class="a__char a__char--1 js-char astro-rdpcjfuz"> 1 </span><span class="a__char a__char--1 js-char astro-rdpcjfuz"> 1 </span><span class="a__char a__char--1 js-char astro-rdpcjfuz"> 1 </span><span class="a__char a__char--blank js-char astro-rdpcjfuz"> </span><span class="a__char a__char--1 js-char astro-rdpcjfuz"> 1 </span><span class="a__char a__char--1 js-char astro-rdpcjfuz"> 1 </span><span class="a__char a__char--0 js-char astro-rdpcjfuz"> 0 </span><span class="a__char a__char--0 js-char astro-rdpcjfuz"> 0 </span><span class="a__char a__char--0 js-char astro-rdpcjfuz"> 0 </span><span class="a__char a__char--0 js-char astro-rdpcjfuz"> 0 </span><span class="a__char a__char--1 js-char astro-rdpcjfuz"> 1 </span><span class="a__char a__char--blank js-char astro-rdpcjfuz"> </span><span class="a__char a__char--1 js-char astro-rdpcjfuz"> 1 </span><span class="a__char a__char--1 js-char astro-rdpcjfuz"> 1 </span><span class="a__char a__char--1 js-char astro-rdpcjfuz"> 1 </span><span class="a__char a__char--1 js-char astro-rdpcjfuz"> 1 </span><span class="a__char a__char--0 js-char astro-rdpcjfuz"> 0 </span><span class="a__char a__char--0 js-char astro-rdpcjfuz"> 0 </span><span class="a__char a__char--1 js-char astro-rdpcjfuz"> 1 </span> </span> <span class="a__stripes astro-rdpcjfuz"></span> </span> <span class="a__triangle astro-rdpcjfuz"></span> </a-separator> <section class="s-my-way astro-fiibiggq is-out-of-view is-out-of-view-bottom" data-intersect="" style="--distortion: 180.45941621317522; --scroll-progress: 0;"> <svg class="s__smiley js-smiley astro-fiibiggq" viewbox="0 0 77.8 77.8" xml:space="preserve" xmlns="http://www.w3.org/2000/svg"> <circle class="astro-fiibiggq" cx="38.9" cy="38.9" r="38.9"></circle> <path class="astro-fiibiggq" d="M38.9 77.8c-2 0-4.1-.2-6.2-.5C11.6 73.9-2.9 53.9.5 32.8 2.1 22.5 7.7 13.5 16.1 7.4c8.4-6.1 18.7-8.5 29-6.9 10.3 1.6 19.3 7.2 25.4 15.6 6.1 8.4 8.5 18.7 6.9 29-3.1 19.1-19.7 32.7-38.5 32.7zM38.8 1c-7.9 0-15.6 2.5-22.1 7.2C8.5 14.1 3.1 22.9 1.5 32.9-1.8 53.5 12.3 73 32.9 76.3 53.5 79.6 73 65.5 76.3 44.9l.5.1-.5-.1c1.6-10-.8-20-6.7-28.2S54.9 3.1 44.9 1.5c-2-.3-4.1-.5-6.1-.5zM25.5 23.1c-1.9 0-3.5 2-4.1 5.1l-.1.3 3 2.2-2.9 2.2.1.3c.6 2.5 1.5 5.1 4.1 5.1 2.4 0 4.2-3.3 4.2-7.6s-2.4-7.6-4.3-7.6zm26.6 0c-1.9 0-3.5 2-4.1 5.1v.3l3 2.2-3 2.2.1.3c.6 2.5 1.5 5.1 4.1 5.1 2.4 0 4.2-3.3 4.2-7.6s-2.3-7.6-4.3-7.6zM62 39c0-.3-.2-.5-.5-.5s-.5.2-.5.5c0 12.2-9.9 22.1-22.1 22.1-12.2 0-22.1-9.9-22.1-22.1 0-.3-.2-.5-.5-.5s-.5.2-.5.5c0 12.7 10.4 23.1 23.1 23.1S62 51.7 62 39z"></path> </svg> <div class="s__objects js-objects astro-fiibiggq" style="perspective-origin: 50% 1055px;"> <div class="a-object a-object--frame astro-fiibiggq"> <figure class="a__inner astro-fiibiggq"> <img alt="Generative art poster concept" class="a__img astro-fiibiggq" decoding="async" height="1024" loading="lazy" src="/_astro/art-1987.DuGYX_YQ_Zedl3o.webp" width="1024"/> <figcaption class="a__caption astro-fiibiggq">Generative art poster concept</figcaption> </figure> <div class="a__side a__side--vertical astro-fiibiggq"></div> <div class="a__side a__side--horizontal astro-fiibiggq"></div> </div><div class="a-object a-object--frame astro-fiibiggq"> <figure class="a__inner astro-fiibiggq"> <img alt="Generative art poster concept" class="a__img astro-fiibiggq" decoding="async" height="1024" loading="lazy" src="/_astro/art-dtyw.BwdKK6hB_Z19TwfB.webp" width="760"/> <figcaption class="a__caption astro-fiibiggq">Generative art poster concept</figcaption> </figure> <div class="a__side a__side--vertical astro-fiibiggq"></div> <div class="a__side a__side--horizontal astro-fiibiggq"></div> </div><div class="a-object a-object--frame astro-fiibiggq"> <figure class="a__inner astro-fiibiggq"> <img alt="Generative art poster concept" class="a__img astro-fiibiggq" decoding="async" height="1024" loading="lazy" src="/_astro/art-lines.BXTmZZe3_Z1DHEgE.webp" width="802"/> <figcaption class="a__caption astro-fiibiggq">Generative art poster concept</figcaption> </figure> <div class="a__side a__side--vertical astro-fiibiggq"></div> <div class="a__side a__side--horizontal astro-fiibiggq"></div> </div><div class="a-object a-object--frame astro-fiibiggq"> <figure class="a__inner astro-fiibiggq"> <img alt="My first FOTD on FWA  ♥ (2012)" class="a__img astro-fiibiggq" decoding="async" height="1024" loading="lazy" src="/_astro/first-fwa.LsgJSoFn_1VhWNL.webp" width="768"/> <figcaption class="a__caption astro-fiibiggq">My first FOTD on FWA  ♥ (2012)</figcaption> </figure> <div class="a__side a__side--vertical astro-fiibiggq"></div> <div class="a__side a__side--horizontal astro-fiibiggq"></div> </div><div class="a-object a-object--frame astro-fiibiggq"> <figure class="a__inner astro-fiibiggq"> <img alt="Young me discovering the beauty of &lt;s&gt;Grand Canyon&lt;/s&gt; Tetris (1997)" class="a__img astro-fiibiggq" decoding="async" height="600" loading="lazy" src="/_astro/gameboy.BbEYkrsC_RRGJe.webp" width="600"/> <figcaption class="a__caption astro-fiibiggq">Young me discovering the beauty of <s>Grand Canyon</s> Tetris (1997)</figcaption> </figure> <div class="a__side a__side--vertical astro-fiibiggq"></div> <div class="a__side a__side--horizontal astro-fiibiggq"></div> </div><div class="a-object a-object--frame astro-fiibiggq"> <figure class="a__inner astro-fiibiggq"> <img alt="Me abusing of remote work (2005)" class="a__img astro-fiibiggq" decoding="async" height="1024" loading="lazy" src="/_astro/remote-2005.B2CSTJrO_1R04cN.webp" width="768"/> <figcaption class="a__caption astro-fiibiggq">Me abusing of remote work (2005)</figcaption> </figure> <div class="a__side a__side--vertical astro-fiibiggq"></div> <div class="a__side a__side--horizontal astro-fiibiggq"></div> </div><div class="a-object a-object--frame astro-fiibiggq" style="--size: 0.9292526631684055; --s: 1;"> <figure class="a__inner astro-fiibiggq"> <img alt="Roaaaar!" class="a__img astro-fiibiggq" decoding="async" height="1024" loading="lazy" src="/_astro/roar.BvXyAVaL_1PKGBj.webp" width="576"/> <figcaption class="a__caption astro-fiibiggq">Roaaaar!</figcaption> </figure> <div class="a__side a__side--vertical astro-fiibiggq"></div> <div class="a__side a__side--horizontal astro-fiibiggq"></div> </div><div class="a-object a-object--frame astro-fiibiggq"> <figure class="a__inner astro-fiibiggq"> <img alt="Early age (2006) desk setup " class="a__img astro-fiibiggq" decoding="async" height="480" loading="lazy" src="/_astro/setup-2006.Op2RjVqP_ZjqlNh.webp" width="640"/> <figcaption class="a__caption astro-fiibiggq">Early age (2006) desk setup </figcaption> </figure> <div class="a__side a__side--vertical astro-fiibiggq"></div> <div class="a__side a__side--horizontal astro-fiibiggq"></div> </div><div class="a-object a-object--frame astro-fiibiggq"> <figure class="a__inner astro-fiibiggq"> <img alt="2016 desk setup" class="a__img astro-fiibiggq" decoding="async" height="576" loading="lazy" src="/_astro/setup-2016.DZszJSwz_10aiku.webp" width="1024"/> <figcaption class="a__caption astro-fiibiggq">2016 desk setup</figcaption> </figure> <div class="a__side a__side--vertical astro-fiibiggq"></div> <div class="a__side a__side--horizontal astro-fiibiggq"></div> </div><div class="a-object a-object--frame astro-fiibiggq" style="--size: 0.8769904022901497; --s: 1;"> <figure class="a__inner astro-fiibiggq"> <img alt="2020 desk setup" class="a__img astro-fiibiggq" decoding="async" height="576" loading="lazy" src="/_astro/setup-2020.DjuS52Ke_1lqvvK.webp" width="1024"/> <figcaption class="a__caption astro-fiibiggq">2020 desk setup</figcaption> </figure> <div class="a__side a__side--vertical astro-fiibiggq"></div> <div class="a__side a__side--horizontal astro-fiibiggq"></div> </div><div class="a-object a-object--frame astro-fiibiggq"> <figure class="a__inner astro-fiibiggq"> <img alt="Waaark Creative Robots" class="a__img astro-fiibiggq" decoding="async" height="1440" loading="lazy" src="/_astro/waaark.C5QpwSMH_Rq3S9.webp" width="1440"/> <figcaption class="a__caption astro-fiibiggq">Waaark Creative Robots</figcaption> </figure> <div class="a__side a__side--vertical astro-fiibiggq"></div> <div class="a__side a__side--horizontal astro-fiibiggq"></div> </div><div class="a-object a-object--frame astro-fiibiggq"> <figure class="a__inner astro-fiibiggq"> <img alt="2011 portfolio" class="a__img astro-fiibiggq" decoding="async" height="768" loading="lazy" src="/_astro/portfolio-2011.DpFoQfUQ_ZXEwJ9.webp" width="1024"/> <figcaption class="a__caption astro-fiibiggq">2011 portfolio</figcaption> </figure> <div class="a__side a__side--vertical astro-fiibiggq"></div> <div class="a__side a__side--horizontal astro-fiibiggq"></div> </div><div class="a-object a-object--frame astro-fiibiggq" style="--size: 0.8722971361771514; --s: 1;"> <figure class="a__inner astro-fiibiggq"> <img alt="2014 portfolio" class="a__img astro-fiibiggq" decoding="async" height="767" loading="lazy" src="/_astro/portfolio-2014.ClRt5L9z_Xz3cl.webp" width="1024"/> <figcaption class="a__caption astro-fiibiggq">2014 portfolio</figcaption> </figure> <div class="a__side a__side--vertical astro-fiibiggq"></div> <div class="a__side a__side--horizontal astro-fiibiggq"></div> </div><div class="a-object a-object--frame astro-fiibiggq"> <figure class="a__inner astro-fiibiggq"> <img alt="2017 portfolio (never released)" class="a__img astro-fiibiggq" decoding="async" height="768" loading="lazy" src="/_astro/portfolio-2017.N-r3CKDK_iyXU7.webp" width="1024"/> <figcaption class="a__caption astro-fiibiggq">2017 portfolio (never released)</figcaption> </figure> <div class="a__side a__side--vertical astro-fiibiggq"></div> <div class="a__side a__side--horizontal astro-fiibiggq"></div> </div><div class="a-object a-object--frame astro-fiibiggq"> <figure class="a__inner astro-fiibiggq"> <img alt="2021 portfolio" class="a__img astro-fiibiggq" decoding="async" height="768" loading="lazy" src="/_astro/portfolio-2021.D5EtPDWp_Z1AfsSn.webp" width="1024"/> <figcaption class="a__caption astro-fiibiggq">2021 portfolio</figcaption> </figure> <div class="a__side a__side--vertical astro-fiibiggq"></div> <div class="a__side a__side--horizontal astro-fiibiggq"></div> </div><div class="a-object a-object--frame astro-fiibiggq"> <figure class="a__inner astro-fiibiggq"> <img alt="Legos ♥" class="a__img astro-fiibiggq" decoding="async" height="1024" loading="lazy" src="/_astro/legos.Bdikeciy_Z1pUyVv.webp" width="768"/> <figcaption class="a__caption astro-fiibiggq">Legos ♥</figcaption> </figure> <div class="a__side a__side--vertical astro-fiibiggq"></div> <div class="a__side a__side--horizontal astro-fiibiggq"></div> </div> <div class="a-object a-object--star astro-fiibiggq" style="--size: 0.8886916705619907; --s: 1;"> <div class="a__side a__side--top-left astro-fiibiggq"></div> <div class="a__side a__side--top-right astro-fiibiggq"></div> <div class="a__side a__side--bottom-left astro-fiibiggq"></div> <div class="a__side a__side--bottom-right astro-fiibiggq"></div> </div><div class="a-object a-object--star astro-fiibiggq" style="--size: 0.6522559091106992; --s: 1;"> <div class="a__side a__side--top-left astro-fiibiggq"></div> <div class="a__side a__side--top-right astro-fiibiggq"></div> <div class="a__side a__side--bottom-left astro-fiibiggq"></div> <div class="a__side a__side--bottom-right astro-fiibiggq"></div> </div><div class="a-object a-object--star astro-fiibiggq"> <div class="a__side a__side--top-left astro-fiibiggq"></div> <div class="a__side a__side--top-right astro-fiibiggq"></div> <div class="a__side a__side--bottom-left astro-fiibiggq"></div> <div class="a__side a__side--bottom-right astro-fiibiggq"></div> </div><div class="a-object a-object--star astro-fiibiggq"> <div class="a__side a__side--top-left astro-fiibiggq"></div> <div class="a__side a__side--top-right astro-fiibiggq"></div> <div class="a__side a__side--bottom-left astro-fiibiggq"></div> <div class="a__side a__side--bottom-right astro-fiibiggq"></div> </div><div class="a-object a-object--star astro-fiibiggq"> <div class="a__side a__side--top-left astro-fiibiggq"></div> <div class="a__side a__side--top-right astro-fiibiggq"></div> <div class="a__side a__side--bottom-left astro-fiibiggq"></div> <div class="a__side a__side--bottom-right astro-fiibiggq"></div> </div><div class="a-object a-object--star astro-fiibiggq"> <div class="a__side a__side--top-left astro-fiibiggq"></div> <div class="a__side a__side--top-right astro-fiibiggq"></div> <div class="a__side a__side--bottom-left astro-fiibiggq"></div> <div class="a__side a__side--bottom-right astro-fiibiggq"></div> </div><div class="a-object a-object--star astro-fiibiggq"> <div class="a__side a__side--top-left astro-fiibiggq"></div> <div class="a__side a__side--top-right astro-fiibiggq"></div> <div class="a__side a__side--bottom-left astro-fiibiggq"></div> <div class="a__side a__side--bottom-right astro-fiibiggq"></div> </div><div class="a-object a-object--star astro-fiibiggq"> <div class="a__side a__side--top-left astro-fiibiggq"></div> <div class="a__side a__side--top-right astro-fiibiggq"></div> <div class="a__side a__side--bottom-left astro-fiibiggq"></div> <div class="a__side a__side--bottom-right astro-fiibiggq"></div> </div><div class="a-object a-object--star astro-fiibiggq"> <div class="a__side a__side--top-left astro-fiibiggq"></div> <div class="a__side a__side--top-right astro-fiibiggq"></div> <div class="a__side a__side--bottom-left astro-fiibiggq"></div> <div class="a__side a__side--bottom-right astro-fiibiggq"></div> </div><div class="a-object a-object--star astro-fiibiggq"> <div class="a__side a__side--top-left astro-fiibiggq"></div> <div class="a__side a__side--top-right astro-fiibiggq"></div> <div class="a__side a__side--bottom-left astro-fiibiggq"></div> <div class="a__side a__side--bottom-right astro-fiibiggq"></div> </div> <div class="s__ruler js-ruler astro-fiibiggq"></div> </div><!-- .s__objects --> <div class="s__catcher astro-fiibiggq"> <div class="s__catcher__distorted-wrapper astro-fiibiggq"> <div class="s__catcher__distorted astro-fiibiggq"> <div class="s__catcher__text s__catcher__text--distorted astro-fiibiggq">
Coding <br class="astro-fiibiggq"/>
my way <br class="astro-fiibiggq"/>
since <br class="astro-fiibiggq"/>
1987
</div> </div><!-- .s__catcher__distorted --> </div><!-- .s__catcher__distorted-wrapper --> <div class="s__catcher__normal-wrapper astro-fiibiggq"> <div class="s__catcher__normal astro-fiibiggq"> <div class="s__catcher__text s__catcher__text--normal astro-fiibiggq">
Coding <br class="astro-fiibiggq"/>
my way <br class="astro-fiibiggq"/>
since <br class="astro-fiibiggq"/>
1987
</div> </div><!-- .s__catcher__normal --> </div><!-- .s__catcher__normal-wrapper --> </div><!-- .s__catcher --> <svg class="s__svg js-svg astro-fiibiggq" style="width: 1248px; height: 990px;"> <path class="s__svg__circular-path js-lines-circular-path astro-fiibiggq" d="M 0 990 L 1248 990M 0 0 L 624.0000419616699 345 M 0 990 L 624.0000419616699 345 M 104 0 L 624.0000419616699 345 M 104 990 L 624.0000419616699 345 M 208 0 L 624.0000419616699 345 M 208 990 L 624.0000419616699 345 M 312 0 L 624.0000419616699 345 M 312 990 L 624.0000419616699 345 M 416 0 L 624.0000419616699 345 M 416 990 L 624.0000419616699 345 M 520 0 L 624.0000419616699 345 M 520 990 L 624.0000419616699 345 M 624 0 L 624.0000419616699 345 M 624 990 L 624.0000419616699 345 M 728 0 L 624.0000419616699 345 M 728 990 L 624.0000419616699 345 M 832 0 L 624.0000419616699 345 M 832 990 L 624.0000419616699 345 M 936 0 L 624.0000419616699 345 M 936 990 L 624.0000419616699 345 M 1040 0 L 624.0000419616699 345 M 1040 990 L 624.0000419616699 345 M 1144 0 L 624.0000419616699 345 M 1144 990 L 624.0000419616699 345 M 1248 0 L 624.0000419616699 345 M 1248 990 L 624.0000419616699 345 M 0 82.5 L 624.0000419616699 345 M 1248 82.5 L 624.0000419616699 345 M 0 165 L 624.0000419616699 345 M 1248 165 L 624.0000419616699 345 M 0 247.5 L 624.0000419616699 345 M 1248 247.5 L 624.0000419616699 345 M 0 330 L 624.0000419616699 345 M 1248 330 L 624.0000419616699 345 M 0 412.5 L 624.0000419616699 345 M 1248 412.5 L 624.0000419616699 345 M 0 495 L 624.0000419616699 345 M 1248 495 L 624.0000419616699 345 M 0 577.5 L 624.0000419616699 345 M 1248 577.5 L 624.0000419616699 345 M 0 660 L 624.0000419616699 345 M 1248 660 L 624.0000419616699 345 M 0 742.5 L 624.0000419616699 345 M 1248 742.5 L 624.0000419616699 345 M 0 825 L 624.0000419616699 345 M 1248 825 L 624.0000419616699 345 M 0 907.5 L 624.0000419616699 345 M 1248 907.5 L 624.0000419616699 345 "></path> </svg> </section> <section class="s-cta astro-25kxajgh is-out-of-view is-out-of-view-bottom" data-intersect=""> <div class="s__inner js-container astro-25kxajgh" id="contact"> <div class="s__hover js-hover astro-25kxajgh"> <div class="s__button js-button astro-25kxajgh"> <div class="s__button__inner astro-25kxajgh"> <!-- .s__button__text --> <div class="s__button__text js-button-text astro-25kxajgh" style="translate: none; rotate: none; scale: none; transform: translate3d(0px, 0px, 0px) scale(0.9944, 0.9944);">
GO
</div></div><!-- .s__button__inner --> </div><!-- .s__button --> <div class="s__cta js-cta astro-25kxajgh" style="--size: 558px;"> <div class="s__cta__line s__cta__line--top astro-25kxajgh"> <div class="s__cta__text astro-25kxajgh"> <span class="s__cta__char astro-25kxajgh"> <span class="s__cta__char__slice astro-25kxajgh">L</span><span class="s__cta__char__slice astro-25kxajgh">L</span><span class="s__cta__char__slice astro-25kxajgh">L</span><span class="s__cta__char__slice astro-25kxajgh">L</span> </span><span class="s__cta__char astro-25kxajgh"> <span class="s__cta__char__slice astro-25kxajgh">e</span><span class="s__cta__char__slice astro-25kxajgh">e</span><span class="s__cta__char__slice astro-25kxajgh">e</span><span class="s__cta__char__slice astro-25kxajgh">e</span> </span><span class="s__cta__char astro-25kxajgh"> <span class="s__cta__char__slice astro-25kxajgh">t<span class="sup">'</span></span><span class="s__cta__char__slice astro-25kxajgh">t<span class="sup">'</span></span><span class="s__cta__char__slice astro-25kxajgh">t<span class="sup">'</span></span><span class="s__cta__char__slice astro-25kxajgh">t<span class="sup">'</span></span> </span><span class="s__cta__char astro-25kxajgh"> <span class="s__cta__char__slice astro-25kxajgh">s</span><span class="s__cta__char__slice astro-25kxajgh">s</span><span class="s__cta__char__slice astro-25kxajgh">s</span><span class="s__cta__char__slice astro-25kxajgh">s</span> </span> </div> </div><div class="s__cta__line s__cta__line--bottom astro-25kxajgh"> <div class="s__cta__text astro-25kxajgh"> <span class="s__cta__char astro-25kxajgh"> <span class="s__cta__char__slice astro-25kxajgh">R</span><span class="s__cta__char__slice astro-25kxajgh">R</span><span class="s__cta__char__slice astro-25kxajgh">R</span><span class="s__cta__char__slice astro-25kxajgh">R</span> </span><span class="s__cta__char astro-25kxajgh"> <span class="s__cta__char__slice astro-25kxajgh">o</span><span class="s__cta__char__slice astro-25kxajgh">o</span><span class="s__cta__char__slice astro-25kxajgh">o</span><span class="s__cta__char__slice astro-25kxajgh">o</span> </span><span class="s__cta__char astro-25kxajgh"> <span class="s__cta__char__slice astro-25kxajgh">c</span><span class="s__cta__char__slice astro-25kxajgh">c</span><span class="s__cta__char__slice astro-25kxajgh">c</span><span class="s__cta__char__slice astro-25kxajgh">c</span> </span><span class="s__cta__char astro-25kxajgh"> <span class="s__cta__char__slice astro-25kxajgh">k</span><span class="s__cta__char__slice astro-25kxajgh">k</span><span class="s__cta__char__slice astro-25kxajgh">k</span><span class="s__cta__char__slice astro-25kxajgh">k</span> </span> </div> </div> <a class="s__cta__link astro-25kxajgh" href="mailto:<EMAIL>">
<EMAIL>
</a> <div class="s__cta__stars astro-25kxajgh"> <svg class="s__cta__star astro-25kxajgh" fill="none" height="49" viewbox="0 0 49 49" width="49" xmlns="http://www.w3.org/2000/svg"> <path class="astro-25kxajgh" d="m24.5 0 3.3 21.2L49 24.5l-21.2 3.3L24.5 49l-3.3-21.2L0 24.5l21.2-3.3L24.5 0z"></path> </svg> <svg class="s__cta__star astro-25kxajgh" fill="none" height="49" viewbox="0 0 49 49" width="49" xmlns="http://www.w3.org/2000/svg"> <path class="astro-25kxajgh" d="m24.5 0 3.3 21.2L49 24.5l-21.2 3.3L24.5 49l-3.3-21.2L0 24.5l21.2-3.3L24.5 0z"></path> </svg> <svg class="s__cta__star astro-25kxajgh" fill="none" height="49" viewbox="0 0 49 49" width="49" xmlns="http://www.w3.org/2000/svg"> <path class="astro-25kxajgh" d="m24.5 0 3.3 21.2L49 24.5l-21.2 3.3L24.5 49l-3.3-21.2L0 24.5l21.2-3.3L24.5 0z"></path> </svg> <svg class="s__cta__star astro-25kxajgh" fill="none" height="49" viewbox="0 0 49 49" width="49" xmlns="http://www.w3.org/2000/svg"> <path class="astro-25kxajgh" d="m24.5 0 3.3 21.2L49 24.5l-21.2 3.3L24.5 49l-3.3-21.2L0 24.5l21.2-3.3L24.5 0z"></path> </svg> </div> <div class="a-dots astro-25kxajgh"></div> </div><!-- .s__cta --> </div><!-- .s__hover --> </div><!-- .s__inner --> <div class="s__grid js-grid astro-25kxajgh"> <svg class="s__grid__svg js-grid-svg astro-25kxajgh" style="width: 1250px; height: 1142px;"> <path class="s__grid__path js-grid-path astro-25kxajgh" d=""></path> </svg> </div><!-- .s__grid --> </section> <footer class="site-foot astro-75cegwoc"> <div class="site-foot__logo astro-75cegwoc"> <a class="astro-75cegwoc" href="/"> <svg class="astro-75cegwoc" fill="none" height="280" viewbox="0 0 280 280" width="280" xmlns="http://www.w3.org/2000/svg"> <path class="astro-75cegwoc" d="M240.245 0v263.2h-19.894V0h-39.756v263.2h-19.861V0h-39.755v280H280V0h-39.755Z" fill="#160000"></path> <path class="astro-75cegwoc" d="M0 0v280h39.755V16.8H59.65V280h39.756V0H0Z" fill="#160000"></path> </svg> <span class="u-sr-only astro-75cegwoc">Antoine Wodniack</span> </a> </div><!-- .site-foot__logo --> </footer> </div><!-- .site-wrapper --> <!-- .site-intro --> <div class="site-mount js-mount astro-j7pv25f6" style="opacity: 1;"></div> <div class="site-contrast-mask js-contrast-mask astro-j7pv25f6"></div> <div class="site-scrollbar astro-yxqriywh" style="--scrollbar-height: 29.006266786034022px; --scrollbar-top: 0px;"> <div class="site-scrollbar__track astro-yxqriywh"></div> <div class="site-scrollbar__thumb js-thumb astro-yxqriywh"></div> </div> </body></html>