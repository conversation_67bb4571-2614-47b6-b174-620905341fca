# Simple PowerShell script to download OhMyKing homepage
# Usage: powershell -ExecutionPolicy Bypass -File download_simple.ps1

Write-Host "========================================" -ForegroundColor Green
Write-Host "OhMyKing Homepage Downloader" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green
Write-Host ""

# Create directory structure
Write-Host "Creating directories..." -ForegroundColor Yellow
$dirs = @(
    "scraped_website",
    "scraped_website\css",
    "scraped_website\js",
    "scraped_website\js\pages",
    "scraped_website\js\modules", 
    "scraped_website\js\third_party",
    "scraped_website\js\shaders",
    "scraped_website\fonts",
    "scraped_website\images"
)

foreach ($dir in $dirs) {
    if (!(Test-Path $dir)) {
        New-Item -ItemType Directory -Path $dir -Force | Out-Null
    }
}

Write-Host "Directories created!" -ForegroundColor Green
Write-Host ""

# Download function
function Download-File {
    param(
        [string]$Url,
        [string]$Path,
        [string]$Description
    )
    
    try {
        Write-Host "Downloading $Description..." -ForegroundColor Cyan
        $webClient = New-Object System.Net.WebClient
        $webClient.Headers.Add("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36")
        $webClient.DownloadFile($Url, $Path)
        Write-Host "Success: $Description" -ForegroundColor Green
        return $true
    }
    catch {
        Write-Host "Failed: $Description - $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

# Download files
Write-Host "Starting downloads..." -ForegroundColor Yellow
Write-Host ""

$successCount = 0

# Main page
if (Download-File -Url "https://ohmyking.github.io/home/" -Path "scraped_website\index.html" -Description "Main page") {
    $successCount++
}

# CSS files
if (Download-File -Url "https://fonts.googleapis.com/css2?family=Cabin&display=swap" -Path "scraped_website\css\fonts.css" -Description "Font CSS") {
    $successCount++
}
if (Download-File -Url "https://ohmyking.github.io/home/<USER>/styles/styles.css" -Path "scraped_website\css\styles.css" -Description "Main CSS") {
    $successCount++
}

# JS libraries
if (Download-File -Url "https://ohmyking.github.io/home/<USER>/modules/confetti.browser.min.js" -Path "scraped_website\js\confetti.browser.min.js" -Description "Confetti JS") {
    $successCount++
}
if (Download-File -Url "https://ohmyking.github.io/home/<USER>/modules/three.min.js" -Path "scraped_website\js\three.min.js" -Description "Three.js") {
    $successCount++
}
if (Download-File -Url "https://ohmyking.github.io/home/<USER>/modules/tween.umd.js" -Path "scraped_website\js\tween.umd.js" -Description "Tween JS") {
    $successCount++
}
if (Download-File -Url "https://ohmyking.github.io/home/<USER>/modules/gsap.min.js" -Path "scraped_website\js\gsap.min.js" -Description "GSAP JS") {
    $successCount++
}

# Page scripts
if (Download-File -Url "https://ohmyking.github.io/home/<USER>/pages/page4.js" -Path "scraped_website\js\pages\page4.js" -Description "Page 4") {
    $successCount++
}
if (Download-File -Url "https://ohmyking.github.io/home/<USER>/pages/page5.js" -Path "scraped_website\js\pages\page5.js" -Description "Page 5") {
    $successCount++
}
if (Download-File -Url "https://ohmyking.github.io/home/<USER>/pages/page6.js" -Path "scraped_website\js\pages\page6.js" -Description "Page 6") {
    $successCount++
}
if (Download-File -Url "https://ohmyking.github.io/home/<USER>/pages/page7.js" -Path "scraped_website\js\pages\page7.js" -Description "Page 7") {
    $successCount++
}

# Module files
if (Download-File -Url "https://ohmyking.github.io/home/<USER>/modules/renderer.js" -Path "scraped_website\js\modules\renderer.js" -Description "Renderer") {
    $successCount++
}
if (Download-File -Url "https://ohmyking.github.io/home/<USER>/modules/Maf.js" -Path "scraped_website\js\modules\Maf.js" -Description "Maf") {
    $successCount++
}
if (Download-File -Url "https://ohmyking.github.io/home/<USER>/modules/post.js" -Path "scraped_website\js\modules\post.js" -Description "Post") {
    $successCount++
}
if (Download-File -Url "https://ohmyking.github.io/home/<USER>/modules/fbo.js" -Path "scraped_website\js\modules\fbo.js" -Description "FBO") {
    $successCount++
}
if (Download-File -Url "https://ohmyking.github.io/home/<USER>/modules/ShaderPass.js" -Path "scraped_website\js\modules\ShaderPass.js" -Description "ShaderPass") {
    $successCount++
}
if (Download-File -Url "https://ohmyking.github.io/home/<USER>/modules/bloomPass.js" -Path "scraped_website\js\modules\bloomPass.js" -Description "BloomPass") {
    $successCount++
}
if (Download-File -Url "https://ohmyking.github.io/home/<USER>/modules/ShaderPingPongPass.js" -Path "scraped_website\js\modules\ShaderPingPongPass.js" -Description "ShaderPingPongPass") {
    $successCount++
}

# Third party
if (Download-File -Url "https://ohmyking.github.io/home/<USER>/third_party/perlin.js" -Path "scraped_website\js\third_party\perlin.js" -Description "Perlin") {
    $successCount++
}
if (Download-File -Url "https://ohmyking.github.io/home/<USER>/third_party/three.module.js" -Path "scraped_website\js\third_party\three.module.js" -Description "Three Module") {
    $successCount++
}
if (Download-File -Url "https://ohmyking.github.io/home/<USER>/third_party/OrbitControls.js" -Path "scraped_website\js\third_party\OrbitControls.js" -Description "OrbitControls") {
    $successCount++
}

# Shaders
if (Download-File -Url "https://ohmyking.github.io/home/<USER>/shaders/ortho.js" -Path "scraped_website\js\shaders\ortho.js" -Description "Ortho Shader") {
    $successCount++
}
if (Download-File -Url "https://ohmyking.github.io/home/<USER>/shaders/vignette.js" -Path "scraped_website\js\shaders\vignette.js" -Description "Vignette Shader") {
    $successCount++
}
if (Download-File -Url "https://ohmyking.github.io/home/<USER>/shaders/noise.js" -Path "scraped_website\js\shaders\noise.js" -Description "Noise Shader") {
    $successCount++
}
if (Download-File -Url "https://ohmyking.github.io/home/<USER>/shaders/screen.js" -Path "scraped_website\js\shaders\screen.js" -Description "Screen Shader") {
    $successCount++
}
if (Download-File -Url "https://ohmyking.github.io/home/<USER>/shaders/blur.js" -Path "scraped_website\js\shaders\blur.js" -Description "Blur Shader") {
    $successCount++
}
if (Download-File -Url "https://ohmyking.github.io/home/<USER>/shaders/fast-separable-gaussian-blur.js" -Path "scraped_website\js\shaders\fast-separable-gaussian-blur.js" -Description "Gaussian Blur Shader") {
    $successCount++
}

# Font
if (Download-File -Url "https://fonts.gstatic.com/s/cabin/v34/u-4X0qWljRw-PfU81xCKCpdpbgZJl6XFpfEd7eA9BIxxkV2EH7alxw.woff2" -Path "scraped_website\fonts\cabin.woff2" -Description "Font file") {
    $successCount++
}

# Image
if (Download-File -Url "https://ohmyking.github.io/home/<USER>/imgs/card1.png" -Path "scraped_website\images\card1.png" -Description "Card image") {
    $successCount++
}

Write-Host ""
Write-Host "========================================" -ForegroundColor Green
Write-Host "Download Summary" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green
Write-Host "Successfully downloaded: $successCount files" -ForegroundColor Green
Write-Host ""

# Fix HTML paths
if (Test-Path "scraped_website\index.html") {
    Write-Host "Fixing HTML file paths..." -ForegroundColor Yellow
    
    try {
        $htmlContent = Get-Content "scraped_website\index.html" -Raw -Encoding UTF8
        
        # Replace paths
        $htmlContent = $htmlContent -replace 'https://fonts\.googleapis\.com/css2\?family=Cabin&display=swap', 'css/fonts.css'
        $htmlContent = $htmlContent -replace 'src/styles/styles\.css', 'css/styles.css'
        $htmlContent = $htmlContent -replace 'src/modules/', 'js/modules/'
        $htmlContent = $htmlContent -replace 'src/pages/', 'js/pages/'
        $htmlContent = $htmlContent -replace 'src/third_party/', 'js/third_party/'
        $htmlContent = $htmlContent -replace 'src/shaders/', 'js/shaders/'
        $htmlContent = $htmlContent -replace 'src/imgs/', 'images/'
        
        # Save modified file
        $htmlContent | Out-File "scraped_website\index.html" -Encoding UTF8
        Write-Host "HTML paths fixed!" -ForegroundColor Green
    }
    catch {
        Write-Host "Failed to fix HTML paths: $($_.Exception.Message)" -ForegroundColor Red
    }
}

# Fix CSS font paths
if (Test-Path "scraped_website\css\fonts.css") {
    Write-Host "Fixing CSS font paths..." -ForegroundColor Yellow
    
    try {
        $cssContent = Get-Content "scraped_website\css\fonts.css" -Raw -Encoding UTF8
        $cssContent = $cssContent -replace 'https://fonts\.gstatic\.com/s/cabin/v34/u-4X0qWljRw-PfU81xCKCpdpbgZJl6XFpfEd7eA9BIxxkV2EH7alxw\.woff2', '../fonts/cabin.woff2'
        $cssContent | Out-File "scraped_website\css\fonts.css" -Encoding UTF8
        Write-Host "CSS paths fixed!" -ForegroundColor Green
    }
    catch {
        Write-Host "Failed to fix CSS paths: $($_.Exception.Message)" -ForegroundColor Red
    }
}

Write-Host ""
Write-Host "========================================" -ForegroundColor Green
Write-Host "Complete!" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green
Write-Host ""
Write-Host "Files saved to: scraped_website directory" -ForegroundColor White
Write-Host "Open scraped_website\index.html to view the website" -ForegroundColor White
Write-Host ""

# Ask to open file
$response = Read-Host "Open the website now? (y/n)"
if ($response -eq 'y' -or $response -eq 'Y') {
    if (Test-Path "scraped_website\index.html") {
        Start-Process "scraped_website\index.html"
    }
}

Write-Host "Script completed!" -ForegroundColor Green
