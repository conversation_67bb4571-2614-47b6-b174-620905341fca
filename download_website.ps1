# PowerShell脚本：自动下载OhMyKing主页
# 使用方法：在PowerShell中运行 .\download_website.ps1

Write-Host "========================================" -ForegroundColor Green
Write-Host "OhMyKing 主页自动下载脚本" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green
Write-Host ""

# 创建目录结构
Write-Host "创建目录结构..." -ForegroundColor Yellow
$dirs = @(
    "scraped_website",
    "scraped_website\css",
    "scraped_website\js",
    "scraped_website\js\pages",
    "scraped_website\js\modules", 
    "scraped_website\js\third_party",
    "scraped_website\js\shaders",
    "scraped_website\fonts",
    "scraped_website\images"
)

foreach ($dir in $dirs) {
    if (!(Test-Path $dir)) {
        New-Item -ItemType Directory -Path $dir -Force | Out-Null
    }
}

Write-Host "目录创建完成！" -ForegroundColor Green
Write-Host ""

# 定义下载文件列表
$downloads = @(
    @{url="https://ohmyking.github.io/home/"; path="scraped_website\index.html"; desc="主页面"},
    @{url="https://fonts.googleapis.com/css2?family=Cabin&display=swap"; path="scraped_website\css\fonts.css"; desc="字体CSS"},
    @{url="https://ohmyking.github.io/home/<USER>/styles/styles.css"; path="scraped_website\css\styles.css"; desc="样式CSS"},
    @{url="https://ohmyking.github.io/home/<USER>/modules/confetti.browser.min.js"; path="scraped_website\js\confetti.browser.min.js"; desc="confetti.js"},
    @{url="https://ohmyking.github.io/home/<USER>/modules/three.min.js"; path="scraped_website\js\three.min.js"; desc="three.js"},
    @{url="https://ohmyking.github.io/home/<USER>/modules/tween.umd.js"; path="scraped_website\js\tween.umd.js"; desc="tween.js"},
    @{url="https://ohmyking.github.io/home/<USER>/modules/gsap.min.js"; path="scraped_website\js\gsap.min.js"; desc="gsap.js"},
    @{url="https://ohmyking.github.io/home/<USER>/pages/page4.js"; path="scraped_website\js\pages\page4.js"; desc="page4.js"},
    @{url="https://ohmyking.github.io/home/<USER>/pages/page5.js"; path="scraped_website\js\pages\page5.js"; desc="page5.js"},
    @{url="https://ohmyking.github.io/home/<USER>/pages/page6.js"; path="scraped_website\js\pages\page6.js"; desc="page6.js"},
    @{url="https://ohmyking.github.io/home/<USER>/pages/page7.js"; path="scraped_website\js\pages\page7.js"; desc="page7.js"},
    @{url="https://ohmyking.github.io/home/<USER>/modules/renderer.js"; path="scraped_website\js\modules\renderer.js"; desc="renderer.js"},
    @{url="https://ohmyking.github.io/home/<USER>/modules/Maf.js"; path="scraped_website\js\modules\Maf.js"; desc="Maf.js"},
    @{url="https://ohmyking.github.io/home/<USER>/modules/post.js"; path="scraped_website\js\modules\post.js"; desc="post.js"},
    @{url="https://ohmyking.github.io/home/<USER>/modules/fbo.js"; path="scraped_website\js\modules\fbo.js"; desc="fbo.js"},
    @{url="https://ohmyking.github.io/home/<USER>/modules/ShaderPass.js"; path="scraped_website\js\modules\ShaderPass.js"; desc="ShaderPass.js"},
    @{url="https://ohmyking.github.io/home/<USER>/modules/bloomPass.js"; path="scraped_website\js\modules\bloomPass.js"; desc="bloomPass.js"},
    @{url="https://ohmyking.github.io/home/<USER>/modules/ShaderPingPongPass.js"; path="scraped_website\js\modules\ShaderPingPongPass.js"; desc="ShaderPingPongPass.js"},
    @{url="https://ohmyking.github.io/home/<USER>/third_party/perlin.js"; path="scraped_website\js\third_party\perlin.js"; desc="perlin.js"},
    @{url="https://ohmyking.github.io/home/<USER>/third_party/three.module.js"; path="scraped_website\js\third_party\three.module.js"; desc="three.module.js"},
    @{url="https://ohmyking.github.io/home/<USER>/third_party/OrbitControls.js"; path="scraped_website\js\third_party\OrbitControls.js"; desc="OrbitControls.js"},
    @{url="https://ohmyking.github.io/home/<USER>/shaders/ortho.js"; path="scraped_website\js\shaders\ortho.js"; desc="ortho.js"},
    @{url="https://ohmyking.github.io/home/<USER>/shaders/vignette.js"; path="scraped_website\js\shaders\vignette.js"; desc="vignette.js"},
    @{url="https://ohmyking.github.io/home/<USER>/shaders/noise.js"; path="scraped_website\js\shaders\noise.js"; desc="noise.js"},
    @{url="https://ohmyking.github.io/home/<USER>/shaders/screen.js"; path="scraped_website\js\shaders\screen.js"; desc="screen.js"},
    @{url="https://ohmyking.github.io/home/<USER>/shaders/blur.js"; path="scraped_website\js\shaders\blur.js"; desc="blur.js"},
    @{url="https://ohmyking.github.io/home/<USER>/shaders/fast-separable-gaussian-blur.js"; path="scraped_website\js\shaders\fast-separable-gaussian-blur.js"; desc="fast-separable-gaussian-blur.js"},
    @{url="https://fonts.gstatic.com/s/cabin/v34/u-4X0qWljRw-PfU81xCKCpdpbgZJl6XFpfEd7eA9BIxxkV2EH7alxw.woff2"; path="scraped_website\fonts\cabin.woff2"; desc="字体文件"},
    @{url="https://ohmyking.github.io/home/<USER>/imgs/card1.png"; path="scraped_website\images\card1.png"; desc="图片文件"}
)

# 下载文件函数
function Download-File {
    param(
        [string]$Url,
        [string]$Path,
        [string]$Description
    )
    
    try {
        Write-Host "下载 $Description..." -ForegroundColor Cyan
        $webClient = New-Object System.Net.WebClient
        $webClient.Headers.Add("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36")
        $webClient.DownloadFile($Url, $Path)
        Write-Host "✓ 完成: $Description" -ForegroundColor Green
        return $true
    }
    catch {
        Write-Host "✗ 失败: $Description - $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

# 开始下载
Write-Host "开始下载文件..." -ForegroundColor Yellow
Write-Host ""

$totalFiles = $downloads.Count
$successCount = 0

for ($i = 0; $i -lt $downloads.Count; $i++) {
    $download = $downloads[$i]
    $progress = $i + 1
    Write-Host "[$progress/$totalFiles] " -NoNewline -ForegroundColor White
    
    if (Download-File -Url $download.url -Path $download.path -Description $download.desc) {
        $successCount++
    }
}

Write-Host ""
Write-Host "========================================" -ForegroundColor Green
Write-Host "下载统计" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green
Write-Host "总文件数: $totalFiles" -ForegroundColor White
Write-Host "成功下载: $successCount" -ForegroundColor Green
Write-Host "失败数量: $($totalFiles - $successCount)" -ForegroundColor Red
Write-Host ""

# 修改HTML文件中的路径
if (Test-Path "scraped_website\index.html") {
    Write-Host "修改HTML文件中的路径引用..." -ForegroundColor Yellow
    
    try {
        $htmlContent = Get-Content "scraped_website\index.html" -Raw -Encoding UTF8
        
        # 替换路径
        $htmlContent = $htmlContent -replace 'https://fonts\.googleapis\.com/css2\?family=Cabin&display=swap', 'css/fonts.css'
        $htmlContent = $htmlContent -replace 'src/styles/styles\.css', 'css/styles.css'
        $htmlContent = $htmlContent -replace 'src/modules/', 'js/modules/'
        $htmlContent = $htmlContent -replace 'src/pages/', 'js/pages/'
        $htmlContent = $htmlContent -replace 'src/third_party/', 'js/third_party/'
        $htmlContent = $htmlContent -replace 'src/shaders/', 'js/shaders/'
        $htmlContent = $htmlContent -replace 'src/imgs/', 'images/'
        
        # 保存修改后的文件
        $htmlContent | Out-File "scraped_website\index.html" -Encoding UTF8
        Write-Host "✓ HTML路径修改完成" -ForegroundColor Green
    }
    catch {
        Write-Host "✗ HTML路径修改失败: $($_.Exception.Message)" -ForegroundColor Red
    }
}

# 修改CSS文件中的字体路径
if (Test-Path "scraped_website\css\fonts.css") {
    Write-Host "修改CSS文件中的字体路径..." -ForegroundColor Yellow
    
    try {
        $cssContent = Get-Content "scraped_website\css\fonts.css" -Raw -Encoding UTF8
        $cssContent = $cssContent -replace 'https://fonts\.gstatic\.com/s/cabin/v34/u-4X0qWljRw-PfU81xCKCpdpbgZJl6XFpfEd7eA9BIxxkV2EH7alxw\.woff2', '../fonts/cabin.woff2'
        $cssContent | Out-File "scraped_website\css\fonts.css" -Encoding UTF8
        Write-Host "✓ CSS路径修改完成" -ForegroundColor Green
    }
    catch {
        Write-Host "✗ CSS路径修改失败: $($_.Exception.Message)" -ForegroundColor Red
    }
}

Write-Host ""
Write-Host "========================================" -ForegroundColor Green
Write-Host "完成！" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green
Write-Host ""
Write-Host "文件已保存到 scraped_website 目录" -ForegroundColor White
Write-Host "可以直接打开 scraped_website\index.html 查看效果" -ForegroundColor White
Write-Host ""

# 询问是否打开文件
$response = Read-Host "是否现在打开网页？(y/n)"
if ($response -eq 'y' -or $response -eq 'Y') {
    if (Test-Path "scraped_website\index.html") {
        Start-Process "scraped_website\index.html"
    }
}

Write-Host "脚本执行完成！" -ForegroundColor Green
