#!/usr/bin/env python3
"""
Setup and run script for the web scraper
This script will install dependencies and run the scraper
"""

import subprocess
import sys
import os
from pathlib import Path

def run_command(command, description):
    """Run a command and handle errors"""
    print(f"\n{description}...")
    print(f"Running: {command}")
    
    try:
        result = subprocess.run(command, shell=True, check=True, 
                              capture_output=True, text=True)
        if result.stdout:
            print(result.stdout)
        return True
    except subprocess.CalledProcessError as e:
        print(f"Error: {e}")
        if e.stdout:
            print(f"STDOUT: {e.stdout}")
        if e.stderr:
            print(f"STDERR: {e.stderr}")
        return False

def main():
    """Main setup and run function"""
    print("=== Web Scraper Setup and Run ===")
    print("This will scrape https://ohmyking.github.io/home/")
    
    # Check if Python is available
    python_cmd = "python" if sys.platform == "win32" else "python3"
    
    # Install requirements
    if not run_command(f"{python_cmd} -m pip install -r requirements.txt", 
                      "Installing Python dependencies"):
        print("Failed to install dependencies. Please install manually:")
        print(f"{python_cmd} -m pip install -r requirements.txt")
        return
    
    # Install Playwright browsers
    if not run_command(f"{python_cmd} -m playwright install chromium", 
                      "Installing Playwright browser"):
        print("Failed to install Playwright browser. Please install manually:")
        print(f"{python_cmd} -m playwright install chromium")
        return
    
    print("\n=== Setup completed successfully! ===")
    
    # Ask user if they want to run the scraper now
    response = input("\nDo you want to run the scraper now? (y/n): ").lower().strip()
    
    if response in ['y', 'yes']:
        print("\n=== Starting web scraper ===")
        if run_command(f"{python_cmd} web_scraper.py", "Running web scraper"):
            print("\n=== Scraping completed! ===")
            
            # Check if scraped directory exists
            scraped_dir = Path("scraped_ohmyking_home")
            if scraped_dir.exists():
                print(f"\nScraped files are in: {scraped_dir.absolute()}")
                
                # Ask if user wants to start the local server
                response = input("\nDo you want to start the local server to view the scraped website? (y/n): ").lower().strip()
                if response in ['y', 'yes']:
                    server_script = scraped_dir / "server.py"
                    if server_script.exists():
                        print(f"\nStarting local server...")
                        print("The website will open in your browser automatically.")
                        print("Press Ctrl+C to stop the server when you're done.")
                        os.chdir(scraped_dir)
                        subprocess.run([python_cmd, "server.py"])
                    else:
                        print("Server script not found!")
        else:
            print("Scraping failed!")
    else:
        print(f"\nTo run the scraper later, use: {python_cmd} web_scraper.py")
        print("To view scraped content, run the server.py script in the output directory")

if __name__ == "__main__":
    main()
