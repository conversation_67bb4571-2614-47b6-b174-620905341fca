# 手动下载 OhMyKing 主页指南

由于系统没有安装Python，这里提供手动下载的详细指南。

## 需要下载的文件列表

根据网络请求分析，以下是所有需要下载的文件：

### 1. 主页面
- `https://ohmyking.github.io/home/<USER>

### 2. CSS文件
- `https://fonts.googleapis.com/css2?family=Cabin&display=swap` → 保存为 `css/fonts.css`
- `https://ohmyking.github.io/home/<USER>/styles/styles.css` → 保存为 `css/styles.css`

### 3. JavaScript库文件
- `https://ohmyking.github.io/home/<USER>/modules/confetti.browser.min.js` → 保存为 `js/confetti.browser.min.js`
- `https://ohmyking.github.io/home/<USER>/modules/three.min.js` → 保存为 `js/three.min.js`
- `https://ohmyking.github.io/home/<USER>/modules/tween.umd.js` → 保存为 `js/tween.umd.js`
- `https://ohmyking.github.io/home/<USER>/modules/gsap.min.js` → 保存为 `js/gsap.min.js`

### 4. 页面脚本
- `https://ohmyking.github.io/home/<USER>/pages/page4.js` → 保存为 `js/pages/page4.js`
- `https://ohmyking.github.io/home/<USER>/pages/page5.js` → 保存为 `js/pages/page5.js`
- `https://ohmyking.github.io/home/<USER>/pages/page6.js` → 保存为 `js/pages/page6.js`
- `https://ohmyking.github.io/home/<USER>/pages/page7.js` → 保存为 `js/pages/page7.js`

### 5. 模块文件
- `https://ohmyking.github.io/home/<USER>/modules/renderer.js` → 保存为 `js/modules/renderer.js`
- `https://ohmyking.github.io/home/<USER>/modules/Maf.js` → 保存为 `js/modules/Maf.js`
- `https://ohmyking.github.io/home/<USER>/modules/post.js` → 保存为 `js/modules/post.js`
- `https://ohmyking.github.io/home/<USER>/modules/fbo.js` → 保存为 `js/modules/fbo.js`
- `https://ohmyking.github.io/home/<USER>/modules/ShaderPass.js` → 保存为 `js/modules/ShaderPass.js`
- `https://ohmyking.github.io/home/<USER>/modules/bloomPass.js` → 保存为 `js/modules/bloomPass.js`
- `https://ohmyking.github.io/home/<USER>/modules/ShaderPingPongPass.js` → 保存为 `js/modules/ShaderPingPongPass.js`

### 6. 第三方库
- `https://ohmyking.github.io/home/<USER>/third_party/perlin.js` → 保存为 `js/third_party/perlin.js`
- `https://ohmyking.github.io/home/<USER>/third_party/three.module.js` → 保存为 `js/third_party/three.module.js`
- `https://ohmyking.github.io/home/<USER>/third_party/OrbitControls.js` → 保存为 `js/third_party/OrbitControls.js`

### 7. 着色器文件
- `https://ohmyking.github.io/home/<USER>/shaders/ortho.js` → 保存为 `js/shaders/ortho.js`
- `https://ohmyking.github.io/home/<USER>/shaders/vignette.js` → 保存为 `js/shaders/vignette.js`
- `https://ohmyking.github.io/home/<USER>/shaders/noise.js` → 保存为 `js/shaders/noise.js`
- `https://ohmyking.github.io/home/<USER>/shaders/screen.js` → 保存为 `js/shaders/screen.js`
- `https://ohmyking.github.io/home/<USER>/shaders/blur.js` → 保存为 `js/shaders/blur.js`
- `https://ohmyking.github.io/home/<USER>/shaders/fast-separable-gaussian-blur.js` → 保存为 `js/shaders/fast-separable-gaussian-blur.js`

### 8. 字体文件
- `https://fonts.gstatic.com/s/cabin/v34/u-4X0qWljRw-PfU81xCKCpdpbgZJl6XFpfEd7eA9BIxxkV2EH7alxw.woff2` → 保存为 `fonts/cabin.woff2`

### 9. 图片文件
- `https://ohmyking.github.io/home/<USER>/imgs/card1.png` → 保存为 `images/card1.png`

## 目录结构

创建以下目录结构：

```
scraped_website/
├── index.html
├── css/
│   ├── fonts.css
│   └── styles.css
├── js/
│   ├── confetti.browser.min.js
│   ├── three.min.js
│   ├── tween.umd.js
│   ├── gsap.min.js
│   ├── pages/
│   │   ├── page4.js
│   │   ├── page5.js
│   │   ├── page6.js
│   │   └── page7.js
│   ├── modules/
│   │   ├── renderer.js
│   │   ├── Maf.js
│   │   ├── post.js
│   │   ├── fbo.js
│   │   ├── ShaderPass.js
│   │   ├── bloomPass.js
│   │   └── ShaderPingPongPass.js
│   ├── third_party/
│   │   ├── perlin.js
│   │   ├── three.module.js
│   │   └── OrbitControls.js
│   └── shaders/
│       ├── ortho.js
│       ├── vignette.js
│       ├── noise.js
│       ├── screen.js
│       ├── blur.js
│       └── fast-separable-gaussian-blur.js
├── fonts/
│   └── cabin.woff2
└── images/
    └── card1.png
```

## 手动下载步骤

1. **创建目录结构**：按照上面的结构创建所有文件夹

2. **下载主页面**：
   - 在浏览器中访问 `https://ohmyking.github.io/home/<USER>
   - 右键 → "查看页面源代码" 或按 Ctrl+U
   - 复制所有内容并保存为 `index.html`

3. **下载资源文件**：
   - 对于每个URL，在浏览器中直接访问
   - 右键 → "另存为" 保存到对应目录

4. **修改HTML文件**：
   下载完成后，需要修改 `index.html` 中的路径引用：
   
   - 将 `https://fonts.googleapis.com/css2?family=Cabin&display=swap` 改为 `css/fonts.css`
   - 将 `src/styles/styles.css` 改为 `css/styles.css`
   - 将所有 `src/modules/` 改为 `js/modules/`
   - 将所有 `src/pages/` 改为 `js/pages/`
   - 将所有 `src/third_party/` 改为 `js/third_party/`
   - 将所有 `src/shaders/` 改为 `js/shaders/`
   - 将所有 `src/imgs/` 改为 `images/`

5. **修改CSS文件**：
   修改 `css/fonts.css` 中的字体URL：
   - 将字体URL改为 `../fonts/cabin.woff2`

## 快速下载脚本（如果有wget或curl）

如果系统有wget或curl，可以使用以下命令快速下载：

```bash
# 创建目录
mkdir -p scraped_website/{css,js/{pages,modules,third_party,shaders},fonts,images}

# 下载主页面
wget -O scraped_website/index.html "https://ohmyking.github.io/home/"

# 下载CSS
wget -O scraped_website/css/fonts.css "https://fonts.googleapis.com/css2?family=Cabin&display=swap"
wget -O scraped_website/css/styles.css "https://ohmyking.github.io/home/<USER>/styles/styles.css"

# 下载JS库
wget -O scraped_website/js/confetti.browser.min.js "https://ohmyking.github.io/home/<USER>/modules/confetti.browser.min.js"
wget -O scraped_website/js/three.min.js "https://ohmyking.github.io/home/<USER>/modules/three.min.js"
# ... 继续下载其他文件
```

## 验证

下载完成后，直接在浏览器中打开 `index.html` 文件，应该能看到完整的网页效果。

## 注意事项

1. 确保所有文件都下载完整
2. 检查文件路径是否正确
3. 某些文件可能需要特定的MIME类型才能正常工作
4. 如果遇到跨域问题，可能需要通过本地服务器访问
