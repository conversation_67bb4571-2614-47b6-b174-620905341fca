#!/usr/bin/env python3
"""
Download missing WebP images for wodniack.dev
"""

import requests
import os
from pathlib import Path
from urllib.parse import urljoin

def download_missing_images():
    """Download the missing WebP images"""
    
    base_url = "https://wodniack.dev/"
    output_dir = Path("_astro")
    
    # List of missing WebP images from the 404 errors
    missing_images = [
        "art-1987.DuGYX_YQ_Zedl3o.webp",
        "art-dtyw.BwdKK6hB_Z19TwfB.webp", 
        "art-lines.BXTmZZe3_Z1DHEgE.webp",
        "first-fwa.LsgJSoFn_1VhWNL.webp",
        "gameboy.BbEYkrsC_RRGJe.webp",
        "remote-2005.B2CSTJrO_1R04cN.webp",
        "roar.BvXyAVaL_1PKGBj.webp",
        "setup-2006.Op2RjVqP_ZjqlNh.webp"
    ]
    
    print("🖼️  Downloading missing WebP images...")
    print("=" * 50)
    
    session = requests.Session()
    session.headers.update({
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
    })
    
    success_count = 0
    
    for i, image_file in enumerate(missing_images, 1):
        print(f"[{i}/{len(missing_images)}] Downloading: {image_file}")
        
        url = urljoin(base_url, f"_astro/{image_file}")
        local_path = output_dir / image_file
        
        try:
            response = session.get(url, timeout=30)
            response.raise_for_status()
            
            with open(local_path, 'wb') as f:
                f.write(response.content)
            
            print(f"✓ Saved: {local_path}")
            success_count += 1
            
        except Exception as e:
            print(f"✗ Failed to download {image_file}: {e}")
    
    print("=" * 50)
    print(f"Image download completed!")
    print(f"Success: {success_count}/{len(missing_images)}")
    
    if success_count == len(missing_images):
        print("🎉 All missing images downloaded successfully!")
        print("The Awards section should now display properly.")
    else:
        print("⚠️  Some images failed to download.")
        print("The website will still work, but some award images may not display.")

if __name__ == "__main__":
    download_missing_images()
