@echo off
echo ========================================
echo OhMyKing 主页下载脚本
echo ========================================
echo.

REM 创建目录结构
echo 创建目录结构...
mkdir scraped_website 2>nul
mkdir scraped_website\css 2>nul
mkdir scraped_website\js 2>nul
mkdir scraped_website\js\pages 2>nul
mkdir scraped_website\js\modules 2>nul
mkdir scraped_website\js\third_party 2>nul
mkdir scraped_website\js\shaders 2>nul
mkdir scraped_website\fonts 2>nul
mkdir scraped_website\images 2>nul

echo 目录创建完成！
echo.

REM 检查是否有curl命令
curl --version >nul 2>&1
if %errorlevel% neq 0 (
    echo 错误：系统中没有找到 curl 命令
    echo 请手动下载文件，参考 manual_download_guide.md
    pause
    exit /b 1
)

echo 开始下载文件...
echo.

REM 下载主页面
echo [1/29] 下载主页面...
curl -L -o "scraped_website\index.html" "https://ohmyking.github.io/home/"

REM 下载CSS文件
echo [2/29] 下载字体CSS...
curl -L -o "scraped_website\css\fonts.css" "https://fonts.googleapis.com/css2?family=Cabin&display=swap"

echo [3/29] 下载样式CSS...
curl -L -o "scraped_website\css\styles.css" "https://ohmyking.github.io/home/<USER>/styles/styles.css"

REM 下载JavaScript库文件
echo [4/29] 下载 confetti.js...
curl -L -o "scraped_website\js\confetti.browser.min.js" "https://ohmyking.github.io/home/<USER>/modules/confetti.browser.min.js"

echo [5/29] 下载 three.js...
curl -L -o "scraped_website\js\three.min.js" "https://ohmyking.github.io/home/<USER>/modules/three.min.js"

echo [6/29] 下载 tween.js...
curl -L -o "scraped_website\js\tween.umd.js" "https://ohmyking.github.io/home/<USER>/modules/tween.umd.js"

echo [7/29] 下载 gsap.js...
curl -L -o "scraped_website\js\gsap.min.js" "https://ohmyking.github.io/home/<USER>/modules/gsap.min.js"

REM 下载页面脚本
echo [8/29] 下载 page4.js...
curl -L -o "scraped_website\js\pages\page4.js" "https://ohmyking.github.io/home/<USER>/pages/page4.js"

echo [9/29] 下载 page5.js...
curl -L -o "scraped_website\js\pages\page5.js" "https://ohmyking.github.io/home/<USER>/pages/page5.js"

echo [10/29] 下载 page6.js...
curl -L -o "scraped_website\js\pages\page6.js" "https://ohmyking.github.io/home/<USER>/pages/page6.js"

echo [11/29] 下载 page7.js...
curl -L -o "scraped_website\js\pages\page7.js" "https://ohmyking.github.io/home/<USER>/pages/page7.js"

REM 下载模块文件
echo [12/29] 下载 renderer.js...
curl -L -o "scraped_website\js\modules\renderer.js" "https://ohmyking.github.io/home/<USER>/modules/renderer.js"

echo [13/29] 下载 Maf.js...
curl -L -o "scraped_website\js\modules\Maf.js" "https://ohmyking.github.io/home/<USER>/modules/Maf.js"

echo [14/29] 下载 post.js...
curl -L -o "scraped_website\js\modules\post.js" "https://ohmyking.github.io/home/<USER>/modules/post.js"

echo [15/29] 下载 fbo.js...
curl -L -o "scraped_website\js\modules\fbo.js" "https://ohmyking.github.io/home/<USER>/modules/fbo.js"

echo [16/29] 下载 ShaderPass.js...
curl -L -o "scraped_website\js\modules\ShaderPass.js" "https://ohmyking.github.io/home/<USER>/modules/ShaderPass.js"

echo [17/29] 下载 bloomPass.js...
curl -L -o "scraped_website\js\modules\bloomPass.js" "https://ohmyking.github.io/home/<USER>/modules/bloomPass.js"

echo [18/29] 下载 ShaderPingPongPass.js...
curl -L -o "scraped_website\js\modules\ShaderPingPongPass.js" "https://ohmyking.github.io/home/<USER>/modules/ShaderPingPongPass.js"

REM 下载第三方库
echo [19/29] 下载 perlin.js...
curl -L -o "scraped_website\js\third_party\perlin.js" "https://ohmyking.github.io/home/<USER>/third_party/perlin.js"

echo [20/29] 下载 three.module.js...
curl -L -o "scraped_website\js\third_party\three.module.js" "https://ohmyking.github.io/home/<USER>/third_party/three.module.js"

echo [21/29] 下载 OrbitControls.js...
curl -L -o "scraped_website\js\third_party\OrbitControls.js" "https://ohmyking.github.io/home/<USER>/third_party/OrbitControls.js"

REM 下载着色器文件
echo [22/29] 下载 ortho.js...
curl -L -o "scraped_website\js\shaders\ortho.js" "https://ohmyking.github.io/home/<USER>/shaders/ortho.js"

echo [23/29] 下载 vignette.js...
curl -L -o "scraped_website\js\shaders\vignette.js" "https://ohmyking.github.io/home/<USER>/shaders/vignette.js"

echo [24/29] 下载 noise.js...
curl -L -o "scraped_website\js\shaders\noise.js" "https://ohmyking.github.io/home/<USER>/shaders/noise.js"

echo [25/29] 下载 screen.js...
curl -L -o "scraped_website\js\shaders\screen.js" "https://ohmyking.github.io/home/<USER>/shaders/screen.js"

echo [26/29] 下载 blur.js...
curl -L -o "scraped_website\js\shaders\blur.js" "https://ohmyking.github.io/home/<USER>/shaders/blur.js"

echo [27/29] 下载 fast-separable-gaussian-blur.js...
curl -L -o "scraped_website\js\shaders\fast-separable-gaussian-blur.js" "https://ohmyking.github.io/home/<USER>/shaders/fast-separable-gaussian-blur.js"

REM 下载字体文件
echo [28/29] 下载字体文件...
curl -L -o "scraped_website\fonts\cabin.woff2" "https://fonts.gstatic.com/s/cabin/v34/u-4X0qWljRw-PfU81xCKCpdpbgZJl6XFpfEd7eA9BIxxkV2EH7alxw.woff2"

REM 下载图片文件
echo [29/29] 下载图片文件...
curl -L -o "scraped_website\images\card1.png" "https://ohmyking.github.io/home/<USER>/imgs/card1.png"

echo.
echo ========================================
echo 下载完成！
echo ========================================
echo.
echo 文件已保存到 scraped_website 目录
echo.
echo 注意：还需要手动修改 HTML 文件中的路径引用
echo 请参考 manual_download_guide.md 中的说明
echo.
echo 按任意键退出...
pause >nul
