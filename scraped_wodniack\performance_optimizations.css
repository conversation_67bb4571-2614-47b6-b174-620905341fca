/* Performance Optimizations for Wodniack.dev */

/* 1. Video Lazy Loading Styles */
.a__video {
    background-color: #f0f0f0;
    background-image: linear-gradient(45deg, #f0f0f0 25%, transparent 25%), 
                      linear-gradient(-45deg, #f0f0f0 25%, transparent 25%), 
                      linear-gradient(45deg, transparent 75%, #f0f0f0 75%), 
                      linear-gradient(-45deg, transparent 75%, #f0f0f0 75%);
    background-size: 20px 20px;
    background-position: 0 0, 0 10px, 10px -10px, -10px 0px;
    transition: opacity 0.3s ease;
}

.a__video[data-loaded="false"] {
    opacity: 0.7;
}

.a__video[data-loaded="true"] {
    opacity: 1;
    background: none;
}

/* Loading indicator for videos */
.a__video::before {
    content: "Loading...";
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: #666;
    font-size: 14px;
    font-family: monospace;
    z-index: 1;
    pointer-events: none;
}

.a__video[data-loaded="true"]::before {
    display: none;
}

/* 2. Font Loading Optimization */
@font-face {
    font-family: 'PPEditorialNew-Regular';
    src: url('/fonts/PPEditorialNew-Regular.woff2') format('woff2');
    font-display: swap;
    font-weight: normal;
    font-style: normal;
}

@font-face {
    font-family: 'PPEditorialNew-Ultralight';
    src: url('/fonts/PPEditorialNew-Ultralight.woff2') format('woff2');
    font-display: swap;
    font-weight: 100;
    font-style: normal;
}

@font-face {
    font-family: 'PPFraktionMono-Regular';
    src: url('/fonts/PPFraktionMono-Regular.woff2') format('woff2');
    font-display: swap;
    font-weight: normal;
    font-style: normal;
}

@font-face {
    font-family: 'PPFraktionMono-Bold';
    src: url('/fonts/PPFraktionMono-Bold.woff2') format('woff2');
    font-display: swap;
    font-weight: bold;
    font-style: normal;
}

@font-face {
    font-family: 'Bigger-Display';
    src: url('/fonts/Bigger-Display.woff2') format('woff2');
    font-display: swap;
    font-weight: normal;
    font-style: normal;
}

/* 3. Reduce Animation Complexity for Better Performance */
.js-char {
    will-change: transform;
    backface-visibility: hidden;
    transform: translateZ(0);
}

/* 4. Optimize Canvas and SVG Performance */
.s__canvas,
.s__grid {
    will-change: transform;
    transform: translateZ(0);
}

/* 5. Smooth Scrolling Performance */
html {
    scroll-behavior: smooth;
}

/* 6. Reduce Motion for Users Who Prefer It */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
        scroll-behavior: auto !important;
    }
    
    .js-char {
        transform: none !important;
    }
}

/* 7. Performance Hints for Critical Elements */
.s-work {
    contain: layout style paint;
}

.a-work {
    contain: layout style;
}

/* 8. Optimize Binary Character Animations */
.a__char {
    transform: translateZ(0);
    will-change: transform, color;
}

/* 9. Video Container Optimization */
.s__scene__work--video {
    contain: layout style;
}

/* 10. Loading States */
.loading-placeholder {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
}

@keyframes loading {
    0% {
        background-position: 200% 0;
    }
    100% {
        background-position: -200% 0;
    }
}

/* 11. Intersection Observer Optimization */
.is-out-of-view {
    visibility: hidden;
}

.is-in-view {
    visibility: visible;
}

/* 12. GPU Acceleration for Smooth Animations */
.s__scene__letter,
.s__scene__work {
    transform: translateZ(0);
    will-change: transform;
}

/* 13. Optimize Text Rendering */
body {
    text-rendering: optimizeSpeed;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* 14. Critical Path CSS - Above the fold content */
.s-intro {
    contain: layout style paint;
}

/* 15. Memory Management for Large Lists */
.s__objects {
    contain: layout style;
}

.a-object {
    contain: layout;
}
