#!/usr/bin/env python3
"""
Quick fix to add preload="none" to all video tags
"""

import os

def main():
    html_file = 'index.html'
    
    if not os.path.exists(html_file):
        print(f"❌ {html_file} not found!")
        return
    
    print(f"🔧 Quick fixing video tags in {html_file}...")
    
    # Read the HTML file
    with open(html_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Simple replacement
    original_count = content.count('<video class="a__video js-video astro-meqjtcea"')
    content = content.replace(
        'width="1082"></video>',
        'width="1082" preload="none" loading="lazy"></video>'
    )
    
    # Write back to file
    with open(html_file, 'w', encoding='utf-8') as f:
        f.write(content)
    
    new_count = content.count('preload="none"')
    
    print(f"✅ Quick fix complete!")
    print(f"   📹 Total video tags found: {original_count}")
    print(f"   ⚡ Videos with preload='none': {new_count}")

if __name__ == "__main__":
    main()
